  [1m[35mSQL (0.1ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (157.9ms)[0m  [1m[35mCREATE DATABASE "cloudvision_app_development" ENCODING = 'unicode' /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (71.1ms)[0m  [1m[35mCREATE DATABASE "cloudvision_app_test" ENCODING = 'unicode' /*application='CloudvisionApp'*/[0m
  [1m[35m (4.9ms)[0m  [1m[35mCREATE TABLE "schema_migrations" ("version" character varying NOT NULL PRIMARY KEY) /*application='CloudvisionApp'*/[0m
  [1m[35m (2.5ms)[0m  [1m[35mCREATE TABLE "ar_internal_metadata" ("key" character varying NOT NULL PRIMARY KEY, "value" character varying, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.6ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE ActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
  [1m[36mCACHE ActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
  [1m[36mCACHE ActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
  [1m[36mCACHE ActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
  [1m[35m (0.2ms)[0m  [1m[34mSELECT pg_try_advisory_lock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.9ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.8ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Create (0.8ms)[0m  [1m[32mINSERT INTO "ar_internal_metadata" ("key", "value", "created_at", "updated_at") VALUES ('environment', 'development', '2025-08-21 17:51:39.092018', '2025-08-21 17:51:39.092023') RETURNING "key" /*application='CloudvisionApp'*/[0m
Migrating to DeviseCreateUsers (20250821175014)
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[35m (4.6ms)[0m  [1m[35mCREATE TABLE "users" ("id" bigserial primary key, "email" character varying DEFAULT '' NOT NULL, "encrypted_password" character varying DEFAULT '' NOT NULL, "reset_password_token" character varying, "reset_password_sent_at" timestamp(6), "remember_created_at" timestamp(6), "first_name" character varying, "last_name" character varying, "bio" text, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.7ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_users_on_email" ON "users" ("email") /*application='CloudvisionApp'*/[0m
  [1m[35m (1.2ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_users_on_reset_password_token" ON "users" ("reset_password_token") /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (1.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250821175014') RETURNING "version" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.9ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
Migrating to CreateCategories (20250821175020)
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[35m (5.6ms)[0m  [1m[35mCREATE TABLE "categories" ("id" bigserial primary key, "name" character varying, "description" text, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.4ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250821175020') RETURNING "version" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.6ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
Migrating to CreateEntities (20250821175026)
  [1m[36mTRANSACTION (1.3ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[35m (6.5ms)[0m  [1m[35mCREATE TABLE "entities" ("id" bigserial primary key, "name" character varying NOT NULL, "description" text, "address" text, "phone" character varying, "website" character varying, "email" character varying, "latitude" decimal(10,6), "longitude" decimal(10,6), "active" boolean DEFAULT TRUE, "category_id" bigint NOT NULL, "user_id" bigint NOT NULL, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL, CONSTRAINT "fk_rails_3b99dd0cd1"
FOREIGN KEY ("category_id")
  REFERENCES "categories" ("id")
, CONSTRAINT "fk_rails_71e168c975"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
) /*application='CloudvisionApp'*/[0m
  [1m[35m (1.2ms)[0m  [1m[35mCREATE INDEX "index_entities_on_category_id" ON "entities" ("category_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (1.5ms)[0m  [1m[35mCREATE INDEX "index_entities_on_user_id" ON "entities" ("user_id") /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250821175026') RETURNING "version" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
Migrating to CreateReviews (20250821175031)
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[35m (6.3ms)[0m  [1m[35mCREATE TABLE "reviews" ("id" bigserial primary key, "rating" integer NOT NULL, "comment" text, "user_id" bigint NOT NULL, "entity_id" bigint NOT NULL, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL, CONSTRAINT "fk_rails_74a66bd6c5"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
, CONSTRAINT "fk_rails_239809e095"
FOREIGN KEY ("entity_id")
  REFERENCES "entities" ("id")
) /*application='CloudvisionApp'*/[0m
  [1m[35m (1.3ms)[0m  [1m[35mCREATE INDEX "index_reviews_on_user_id" ON "reviews" ("user_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.9ms)[0m  [1m[35mCREATE INDEX "index_reviews_on_entity_id" ON "reviews" ("entity_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.8ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_reviews_on_user_id_and_entity_id" ON "reviews" ("user_id", "entity_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mALTER TABLE "reviews" ADD CONSTRAINT rating_range_check CHECK (rating >= 1 AND rating <= 5) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.3ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250821175031') RETURNING "version" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[34mSELECT pg_advisory_unlock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.3ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.8ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (1.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Restaurants' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (0.4ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Restaurants') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.7ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Restaurants', 'Dining establishments, cafes, and food services', '2025-08-21 17:56:53.026530', '2025-08-21 17:56:53.026530') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.6ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Shops' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.7ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Shops') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.4ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Shops', 'Retail stores and shopping centers', '2025-08-21 17:56:53.044376', '2025-08-21 17:56:53.044376') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.6ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.6ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Entertainment' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.6ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Entertainment') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (1.4ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Entertainment', 'Cinemas, theaters, and entertainment venues', '2025-08-21 17:56:53.074519', '2025-08-21 17:56:53.074519') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.9ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Hotels' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.2ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Hotels') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.6ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Hotels', 'Hotels, motels, and accommodation services', '2025-08-21 17:56:53.095611', '2025-08-21 17:56:53.095611') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.5ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Services' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.2ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Services') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.4ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Services', 'Professional and personal services', '2025-08-21 17:56:53.110847', '2025-08-21 17:56:53.110847') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Health & Fitness' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.2ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Health & Fitness') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.4ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Health & Fitness', 'Gyms, spas, and health services', '2025-08-21 17:56:53.117819', '2025-08-21 17:56:53.117819') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Education' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.1ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Education') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.3ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Education', 'Schools, universities, and educational institutions', '2025-08-21 17:56:53.128453', '2025-08-21 17:56:53.128453') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Automotive' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.2ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Automotive') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.5ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Automotive', 'Car dealerships, repair shops, and automotive services', '2025-08-21 17:56:53.138521', '2025-08-21 17:56:53.138521') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (1.0ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mUser Exists? (0.6ms)[0m  [1m[34mSELECT 1 AS one FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Create (1.0ms)[0m  [1m[32mINSERT INTO "users" ("email", "encrypted_password", "reset_password_token", "reset_password_sent_at", "remember_created_at", "first_name", "last_name", "bio", "created_at", "updated_at") VALUES ('<EMAIL>', '$2a$12$LKph3yiDwSs.ZJGHAIAam.BlPcGCmjXhdhXXPqGjcQ5QbOxJUT2RG', NULL, NULL, NULL, 'John', 'Doe', 'Food enthusiast and local explorer', '2025-08-21 17:56:55.079293', '2025-08-21 17:56:55.079293') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.9ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.4ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mUser Exists? (1.4ms)[0m  [1m[34mSELECT 1 AS one FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Create (0.4ms)[0m  [1m[32mINSERT INTO "users" ("email", "encrypted_password", "reset_password_token", "reset_password_sent_at", "remember_created_at", "first_name", "last_name", "bio", "created_at", "updated_at") VALUES ('<EMAIL>', '$2a$12$08rh.njf/bhqmc6kKVskluHDzrO8r4yr6VKPdsP0yP2wrmnzSRZt2', NULL, NULL, NULL, 'Jane', 'Smith', 'Travel blogger and restaurant critic', '2025-08-21 17:56:55.386487', '2025-08-21 17:56:55.386487') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.7ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.4ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mUser Exists? (1.6ms)[0m  [1m[34mSELECT 1 AS one FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Create (0.3ms)[0m  [1m[32mINSERT INTO "users" ("email", "encrypted_password", "reset_password_token", "reset_password_sent_at", "remember_created_at", "first_name", "last_name", "bio", "created_at", "updated_at") VALUES ('<EMAIL>', '$2a$12$F04nN73gR9NmCp5UfWhCF.JVyZEhohi7ZupGMMzBHI.EU89tU6L3u', NULL, NULL, NULL, 'Mike', 'Johnson', 'Local business owner', '2025-08-21 17:56:55.687823', '2025-08-21 17:56:55.687823') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Restaurants' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.7ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (2.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'The Gourmet Corner' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (2.1ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('The Gourmet Corner', 'Fine dining restaurant with contemporary cuisine and excellent wine selection.', '123 Main Street, Downtown', '(*************', 'https://gourmetcorner.com', '<EMAIL>', NULL, NULL, TRUE, 1, 1, '2025-08-21 17:56:55.757727', '2025-08-21 17:56:55.757727') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Shops' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.1ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Tech Haven Electronics' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (1.1ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('Tech Haven Electronics', 'Your one-stop shop for the latest electronics, gadgets, and tech accessories.', '456 Tech Avenue, Silicon Valley', '(*************', 'https://techhaven.com', '<EMAIL>', NULL, NULL, TRUE, 2, 1, '2025-08-21 17:56:55.770341', '2025-08-21 17:56:55.770341') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Entertainment' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Starlight Cinema' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (1.3ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('Starlight Cinema', 'Modern movie theater with IMAX screens and premium seating options.', '789 Entertainment Blvd, City Center', '(*************', 'https://starlightcinema.com', '<EMAIL>', NULL, NULL, TRUE, 3, 1, '2025-08-21 17:56:55.782453', '2025-08-21 17:56:55.782453') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Hotels' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Grand Plaza Hotel' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (1.3ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('Grand Plaza Hotel', 'Luxury hotel in the heart of the city with world-class amenities.', '321 Hotel Row, Business District', '(*************', 'https://grandplaza.com', '<EMAIL>', NULL, NULL, TRUE, 4, 1, '2025-08-21 17:56:55.792809', '2025-08-21 17:56:55.792809') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.9ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Health & Fitness' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.4ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'FitLife Gym' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (1.3ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('FitLife Gym', 'State-of-the-art fitness center with personal training and group classes.', '654 Fitness Street, Health District', '(*************', 'https://fitlifegym.com', '<EMAIL>', NULL, NULL, TRUE, 6, 1, '2025-08-21 17:56:55.806885', '2025-08-21 17:56:55.806885') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.1ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.9ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 1 AND "reviews"."user_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.4ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 2 AND "reviews"."entity_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (1.7ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (5, 'Very satisfied with the service.', 2, 1, '2025-08-21 17:56:55.869998', '2025-08-21 17:56:55.869998') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[31mROLLBACK /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (1.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (1.1ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Restaurants' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Shops' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.6ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Entertainment' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Hotels' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Services' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Health & Fitness' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Education' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.7ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Automotive' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (1.5ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.4ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (1.7ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Restaurants' LIMIT 1[0m
  [1m[36mUser Load (0.8ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (1.1ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'The Gourmet Corner' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Shops' LIMIT 1[0m
  [1m[36mCACHE User Load (0.0ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1[0m
  [1m[36mEntity Load (0.8ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Tech Haven Electronics' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (0.0ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Entertainment' LIMIT 1[0m
  [1m[36mCACHE User Load (0.0ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1[0m
  [1m[36mEntity Load (0.5ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Starlight Cinema' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (0.0ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Hotels' LIMIT 1[0m
  [1m[36mCACHE User Load (0.0ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1[0m
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Grand Plaza Hotel' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (0.0ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Health & Fitness' LIMIT 1[0m
  [1m[36mCACHE User Load (0.0ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1[0m
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'FitLife Gym' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.7ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 1 AND "reviews"."user_id" = 3 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (2.6ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 3 AND "reviews"."entity_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (1.7ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (4, 'Great experience! Highly recommended.', 3, 1, '2025-08-21 17:58:18.783895', '2025-08-21 17:58:18.783895') RETURNING "id" /*application='CloudvisionApp'*/[0m
  Rendered reviews/_review.html.erb (Duration: 78.1ms | GC: 0.0ms)
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[31mROLLBACK /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (1.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (1.0ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Restaurants' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Shops' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.7ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Entertainment' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.6ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Hotels' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Services' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Health & Fitness' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Education' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Automotive' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (1.3ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.6ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.4ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (1.6ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Restaurants' LIMIT 1[0m
  [1m[36mUser Load (1.1ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (1.1ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'The Gourmet Corner' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Shops' LIMIT 1[0m
  [1m[36mCACHE User Load (0.0ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1[0m
  [1m[36mEntity Load (0.5ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Tech Haven Electronics' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (0.0ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Entertainment' LIMIT 1[0m
  [1m[36mCACHE User Load (0.0ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1[0m
  [1m[36mEntity Load (0.4ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Starlight Cinema' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (0.0ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Hotels' LIMIT 1[0m
  [1m[36mCACHE User Load (0.0ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1[0m
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Grand Plaza Hotel' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (0.0ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Health & Fitness' LIMIT 1[0m
  [1m[36mCACHE User Load (0.0ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1[0m
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'FitLife Gym' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.4ms)[0m  [1m[34mSELECT "entities".* FROM "entities" /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.3ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.5ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 1 AND "reviews"."user_id" = 3 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (2.7ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 3 AND "reviews"."entity_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (1.4ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (3, 'Great experience! Highly recommended.', 3, 1, '2025-08-21 17:59:02.422722', '2025-08-21 17:59:02.422722') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (1.6ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.4ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 2 AND "reviews"."user_id" = 3 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.1ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 3 AND "reviews"."entity_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.8ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (3, 'Great experience! Highly recommended.', 3, 2, '2025-08-21 17:59:02.448498', '2025-08-21 17:59:02.448498') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 2 AND "reviews"."user_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.0ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 2 AND "reviews"."entity_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.7ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (4, 'Very satisfied with the service.', 2, 2, '2025-08-21 17:59:02.458367', '2025-08-21 17:59:02.458367') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 2 AND "reviews"."user_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.0ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 1 AND "reviews"."entity_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.6ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (4, 'Good service and quality. Will visit again.', 1, 2, '2025-08-21 17:59:02.473952', '2025-08-21 17:59:02.473952') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.3ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 3 AND "reviews"."user_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 1 AND "reviews"."entity_id" = 3 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.7ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (3, 'Professional service and great atmosphere.', 1, 3, '2025-08-21 17:59:02.489862', '2025-08-21 17:59:02.489862') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 4 AND "reviews"."user_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (6.5ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 1 AND "reviews"."entity_id" = 4 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.6ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (5, 'Outstanding quality and friendly staff.', 1, 4, '2025-08-21 17:59:02.515435', '2025-08-21 17:59:02.515435') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.6ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.4ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 4 AND "reviews"."user_id" = 3 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.0ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 3 AND "reviews"."entity_id" = 4 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.3ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (4, 'Amazing place! Love coming here.', 3, 4, '2025-08-21 17:59:02.523269', '2025-08-21 17:59:02.523269') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 5 AND "reviews"."user_id" = 3 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.1ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 3 AND "reviews"."entity_id" = 5 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.4ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (3, 'Good service and quality. Will visit again.', 3, 5, '2025-08-21 17:59:02.532266', '2025-08-21 17:59:02.532266') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.4ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 5 AND "reviews"."user_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.8ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 1 AND "reviews"."entity_id" = 5 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.4ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (5, 'Very satisfied with the service.', 1, 5, '2025-08-21 17:59:02.537607', '2025-08-21 17:59:02.537607') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Count (0.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "categories" /*application='CloudvisionApp'*/[0m
  [1m[36mUser Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" /*application='CloudvisionApp'*/[0m
Started HEAD "/" for ::1 at 2025-08-22 00:00:45 +0600
  [1m[36mActiveRecord::SchemaMigration Load (1.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
Processing by HomeController#index as */*
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (2.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (2.7ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.8ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (8.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.7ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.7ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (1.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (1.0ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.6ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.5ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.5ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.7ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.4ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.2ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (1.6ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (1, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.6ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 299.9ms | GC: 0.0ms)
  Rendered layout layouts/application.html.erb (Duration: 318.1ms | GC: 0.0ms)
Completed 200 OK in 479ms (Views: 284.5ms | ActiveRecord: 53.8ms (47 queries, 20 cached) | GC: 0.0ms)


Started GET "/" for ::1 at 2025-08-22 00:01:00 +0600
Processing by HomeController#index as HTML
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.4ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (1.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.3ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (1.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.4ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (1.0ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.5ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (0.3ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (1, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (1.0ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 113.9ms | GC: 17.1ms)
  Rendered layout layouts/application.html.erb (Duration: 121.1ms | GC: 19.8ms)
Completed 200 OK in 162ms (Views: 112.4ms | ActiveRecord: 10.0ms (47 queries, 20 cached) | GC: 19.8ms)


Started GET "/assets/application-8b441ae0.css" for ::1 at 2025-08-22 00:01:00 +0600
Started GET "/assets/turbo.min-3a2e143f.js" for ::1 at 2025-08-22 00:01:00 +0600
Started GET "/assets/stimulus.min-4b1e420e.js" for ::1 at 2025-08-22 00:01:00 +0600
Started GET "/assets/controllers/flash_controller-21fc149c.js" for ::1 at 2025-08-22 00:01:00 +0600
Started GET "/assets/controllers/hello_controller-708796bd.js" for ::1 at 2025-08-22 00:01:00 +0600
Started GET "/assets/controllers/index-ee64e1f1.js" for ::1 at 2025-08-22 00:01:00 +0600
Started GET "/assets/controllers/star_rating_controller-ca4daf08.js" for ::1 at 2025-08-22 00:01:00 +0600
Started GET "/assets/application-bfcdf840.js" for ::1 at 2025-08-22 00:01:01 +0600
Started GET "/assets/stimulus-loading-1fc53fe7.js" for ::1 at 2025-08-22 00:01:01 +0600
Started GET "/assets/controllers/application-3affb389.js" for ::1 at 2025-08-22 00:01:01 +0600
Started GET "/" for ::1 at 2025-08-22 00:01:25 +0600
Processing by HomeController#index as HTML
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.4ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.2ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.1ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.2ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (1, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 43.2ms | GC: 2.3ms)
  Rendered layout layouts/application.html.erb (Duration: 70.8ms | GC: 6.1ms)
Completed 200 OK in 103ms (Views: 65.8ms | ActiveRecord: 5.3ms (47 queries, 20 cached) | GC: 6.1ms)


Started GET "/categories/8" for ::1 at 2025-08-22 00:01:31 +0600
Processing by CategoriesController#show as HTML
  Parameters: {"id" => "8"}
  [1m[36mCategory Load (0.7ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" = 8 LIMIT 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/controllers/categories_controller.rb:15:in 'CategoriesController#set_category'
  Rendering layout layouts/application.html.erb
  Rendering categories/show.html.erb within layouts/application
  Rendered categories/show.html.erb within layouts/application (Duration: 7.3ms | GC: 0.0ms)
  Rendered layout layouts/application.html.erb (Duration: 10.7ms | GC: 0.0ms)
Completed 200 OK in 58ms (Views: 14.2ms | ActiveRecord: 0.7ms (1 query, 0 cached) | GC: 0.0ms)


  [1m[36mCACHE ActiveRecord::SchemaMigration Load (0.8ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.4ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (1.0ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.3ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.2ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (45.4ms)[0m  [1m[35mDROP DATABASE IF EXISTS "cloudvision_app_development" /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (1.1ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (48.4ms)[0m  [1m[35mDROP DATABASE IF EXISTS "cloudvision_app_test" /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (60.9ms)[0m  [1m[35mCREATE DATABASE "cloudvision_app_development" ENCODING = 'unicode' /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (84.2ms)[0m  [1m[35mCREATE DATABASE "cloudvision_app_test" ENCODING = 'unicode' /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mCREATE EXTENSION IF NOT EXISTS "plpgsql" SCHEMA pg_catalog /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE IF EXISTS "categories" CASCADE /*application='CloudvisionApp'*/[0m
  [1m[35m (2.6ms)[0m  [1m[35mCREATE TABLE "categories" ("id" bigserial primary key, "name" character varying, "description" text, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mDROP TABLE IF EXISTS "entities" CASCADE /*application='CloudvisionApp'*/[0m
  [1m[35m (1.5ms)[0m  [1m[35mCREATE TABLE "entities" ("id" bigserial primary key, "name" character varying NOT NULL, "description" text, "address" text, "phone" character varying, "website" character varying, "email" character varying, "latitude" decimal(10,6), "longitude" decimal(10,6), "active" boolean DEFAULT TRUE, "category_id" bigint NOT NULL, "user_id" bigint NOT NULL, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_entities_on_category_id" ON "entities" ("category_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_entities_on_user_id" ON "entities" ("user_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE IF EXISTS "reviews" CASCADE /*application='CloudvisionApp'*/[0m
  [1m[35m (1.3ms)[0m  [1m[35mCREATE TABLE "reviews" ("id" bigserial primary key, "rating" integer NOT NULL, "comment" text, "user_id" bigint NOT NULL, "entity_id" bigint NOT NULL, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL, CONSTRAINT rating_range_check CHECK (rating >= 1 AND rating <= 5)) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_reviews_on_entity_id" ON "reviews" ("entity_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_reviews_on_user_id_and_entity_id" ON "reviews" ("user_id", "entity_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_reviews_on_user_id" ON "reviews" ("user_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "users" CASCADE /*application='CloudvisionApp'*/[0m
  [1m[35m (1.3ms)[0m  [1m[35mCREATE TABLE "users" ("id" bigserial primary key, "email" character varying DEFAULT '' NOT NULL, "encrypted_password" character varying DEFAULT '' NOT NULL, "reset_password_token" character varying, "reset_password_sent_at" timestamp(6), "remember_created_at" timestamp(6), "first_name" character varying, "last_name" character varying, "bio" text, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_users_on_email" ON "users" ("email") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_users_on_reset_password_token" ON "users" ("reset_password_token") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.9ms)[0m  [1m[35mALTER TABLE "entities" ADD CONSTRAINT "fk_rails_3b99dd0cd1"
FOREIGN KEY ("category_id")
  REFERENCES "categories" ("id")
 /*application='CloudvisionApp'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mALTER TABLE "entities" ADD CONSTRAINT "fk_rails_71e168c975"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
 /*application='CloudvisionApp'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mALTER TABLE "reviews" ADD CONSTRAINT "fk_rails_239809e095"
FOREIGN KEY ("entity_id")
  REFERENCES "entities" ("id")
 /*application='CloudvisionApp'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mALTER TABLE "reviews" ADD CONSTRAINT "fk_rails_74a66bd6c5"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
 /*application='CloudvisionApp'*/[0m
  [1m[35m (1.0ms)[0m  [1m[35mCREATE TABLE "schema_migrations" ("version" character varying NOT NULL PRIMARY KEY) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" (version) VALUES (20250821175031) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" (version) VALUES
(20250821175026),
(20250821175020); /*application='CloudvisionApp'*/[0m
  [1m[35m (0.9ms)[0m  [1m[35mCREATE TABLE "ar_internal_metadata" ("key" character varying NOT NULL PRIMARY KEY, "value" character varying, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.2ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Create (0.2ms)[0m  [1m[32mINSERT INTO "ar_internal_metadata" ("key", "value", "created_at", "updated_at") VALUES ('environment', 'development', '2025-08-21 18:12:45.778313', '2025-08-21 18:12:45.778314') RETURNING "key" /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.1ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.0ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'schema_sha1' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Create (0.1ms)[0m  [1m[32mINSERT INTO "ar_internal_metadata" ("key", "value", "created_at", "updated_at") VALUES ('schema_sha1', '195f61e27c90a09af4100253777d3720bbf39ac5', '2025-08-21 18:12:45.783352', '2025-08-21 18:12:45.783353') RETURNING "key" /*application='CloudvisionApp'*/[0m
  [1m[35m (0.0ms)[0m  [1m[34mSELECT pg_try_advisory_lock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.1ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
Migrating to CreateUsers (20250821181218)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[35m (2.3ms)[0m  [1m[35mCREATE TABLE "users" ("id" bigserial primary key, "email" character varying NOT NULL, "password_digest" character varying NOT NULL, "first_name" character varying NOT NULL, "last_name" character varying NOT NULL, "bio" text, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.5ms)[0m  [1m[31mROLLBACK /*application='CloudvisionApp'*/[0m
  [1m[35m (0.0ms)[0m  [1m[34mSELECT pg_advisory_unlock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.3ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.4ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.1ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.1ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (96.9ms)[0m  [1m[35mDROP DATABASE IF EXISTS "cloudvision_app_development" /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (1.1ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (34.7ms)[0m  [1m[35mDROP DATABASE IF EXISTS "cloudvision_app_test" /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (56.2ms)[0m  [1m[35mCREATE DATABASE "cloudvision_app_development" ENCODING = 'unicode' /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (53.6ms)[0m  [1m[35mCREATE DATABASE "cloudvision_app_test" ENCODING = 'unicode' /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mCREATE EXTENSION IF NOT EXISTS "plpgsql" SCHEMA pg_catalog /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE IF EXISTS "categories" CASCADE /*application='CloudvisionApp'*/[0m
  [1m[35m (2.8ms)[0m  [1m[35mCREATE TABLE "categories" ("id" bigserial primary key, "name" character varying, "description" text, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE IF EXISTS "entities" CASCADE /*application='CloudvisionApp'*/[0m
  [1m[35m (1.2ms)[0m  [1m[35mCREATE TABLE "entities" ("id" bigserial primary key, "name" character varying NOT NULL, "description" text, "address" text, "phone" character varying, "website" character varying, "email" character varying, "latitude" decimal(10,6), "longitude" decimal(10,6), "active" boolean DEFAULT TRUE, "category_id" bigint NOT NULL, "user_id" bigint NOT NULL, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_entities_on_category_id" ON "entities" ("category_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_entities_on_user_id" ON "entities" ("user_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "reviews" CASCADE /*application='CloudvisionApp'*/[0m
  [1m[35m (1.4ms)[0m  [1m[35mCREATE TABLE "reviews" ("id" bigserial primary key, "rating" integer NOT NULL, "comment" text, "user_id" bigint NOT NULL, "entity_id" bigint NOT NULL, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL, CONSTRAINT rating_range_check CHECK (rating >= 1 AND rating <= 5)) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_reviews_on_entity_id" ON "reviews" ("entity_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_reviews_on_user_id_and_entity_id" ON "reviews" ("user_id", "entity_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_reviews_on_user_id" ON "reviews" ("user_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "users" CASCADE /*application='CloudvisionApp'*/[0m
  [1m[35m (1.1ms)[0m  [1m[35mCREATE TABLE "users" ("id" bigserial primary key, "email" character varying DEFAULT '' NOT NULL, "encrypted_password" character varying DEFAULT '' NOT NULL, "reset_password_token" character varying, "reset_password_sent_at" timestamp(6), "remember_created_at" timestamp(6), "first_name" character varying, "last_name" character varying, "bio" text, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_users_on_email" ON "users" ("email") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_users_on_reset_password_token" ON "users" ("reset_password_token") /*application='CloudvisionApp'*/[0m
  [1m[35m (1.1ms)[0m  [1m[35mALTER TABLE "entities" ADD CONSTRAINT "fk_rails_3b99dd0cd1"
FOREIGN KEY ("category_id")
  REFERENCES "categories" ("id")
 /*application='CloudvisionApp'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mALTER TABLE "entities" ADD CONSTRAINT "fk_rails_71e168c975"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
 /*application='CloudvisionApp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mALTER TABLE "reviews" ADD CONSTRAINT "fk_rails_239809e095"
FOREIGN KEY ("entity_id")
  REFERENCES "entities" ("id")
 /*application='CloudvisionApp'*/[0m
  [1m[35m (0.7ms)[0m  [1m[35mALTER TABLE "reviews" ADD CONSTRAINT "fk_rails_74a66bd6c5"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
 /*application='CloudvisionApp'*/[0m
  [1m[35m (1.1ms)[0m  [1m[35mCREATE TABLE "schema_migrations" ("version" character varying NOT NULL PRIMARY KEY) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" (version) VALUES (20250821175031) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" (version) VALUES
(20250821175026),
(20250821175020); /*application='CloudvisionApp'*/[0m
  [1m[35m (0.9ms)[0m  [1m[35mCREATE TABLE "ar_internal_metadata" ("key" character varying NOT NULL PRIMARY KEY, "value" character varying, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.3ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Create (0.2ms)[0m  [1m[32mINSERT INTO "ar_internal_metadata" ("key", "value", "created_at", "updated_at") VALUES ('environment', 'development', '2025-08-21 18:22:22.381522', '2025-08-21 18:22:22.381523') RETURNING "key" /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.1ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.0ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'schema_sha1' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Create (0.1ms)[0m  [1m[32mINSERT INTO "ar_internal_metadata" ("key", "value", "created_at", "updated_at") VALUES ('schema_sha1', '195f61e27c90a09af4100253777d3720bbf39ac5', '2025-08-21 18:22:22.387082', '2025-08-21 18:22:22.387082') RETURNING "key" /*application='CloudvisionApp'*/[0m
  [1m[35m (0.0ms)[0m  [1m[34mSELECT pg_try_advisory_lock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.1ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
Migrating to CreateUsers (20250821181218)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[35m (2.5ms)[0m  [1m[35mCREATE TABLE "users" ("id" bigserial primary key, "email" character varying NOT NULL, "password_digest" character varying NOT NULL, "first_name" character varying NOT NULL, "last_name" character varying NOT NULL, "bio" text, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.5ms)[0m  [1m[31mROLLBACK /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[34mSELECT pg_advisory_unlock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[34mSELECT pg_try_advisory_lock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.9ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.5ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
Migrating to ConvertUsersToRailsAuth (20250821182423)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[35m (5.0ms)[0m  [1m[35mALTER TABLE "users" DROP COLUMN "encrypted_password" /*application='CloudvisionApp'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mALTER TABLE "users" DROP COLUMN "reset_password_token" /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mALTER TABLE "users" DROP COLUMN "reset_password_sent_at" /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mALTER TABLE "users" DROP COLUMN "remember_created_at" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[31mROLLBACK /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[34mSELECT pg_advisory_unlock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[34mSELECT pg_try_advisory_lock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (1.9ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.9ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
Migrating to ConvertUsersToRailsAuth (20250821182423)
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[35m (6.6ms)[0m  [1m[35mALTER TABLE "users" DROP COLUMN "encrypted_password" /*application='CloudvisionApp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mALTER TABLE "users" DROP COLUMN "reset_password_token" /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mALTER TABLE "users" DROP COLUMN "reset_password_sent_at" /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mALTER TABLE "users" DROP COLUMN "remember_created_at" /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mALTER TABLE "users" ADD "password_digest" character varying NOT NULL /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mALTER TABLE "users" ALTER COLUMN "first_name" SET NOT NULL /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mALTER TABLE "users" ALTER COLUMN "last_name" SET NOT NULL /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250821182423') RETURNING "version" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.6ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[34mSELECT pg_advisory_unlock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (1.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (1.4ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (1.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Restaurants' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (0.4ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Restaurants') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (1.4ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Restaurants', 'Dining establishments, cafes, and food services', '2025-08-21 18:28:34.814642', '2025-08-21 18:28:34.814642') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.7ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Shops' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.2ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Shops') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.4ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Shops', 'Retail stores and shopping centers', '2025-08-21 18:28:34.847677', '2025-08-21 18:28:34.847677') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.8ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.6ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Entertainment' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.4ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Entertainment') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (1.4ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Entertainment', 'Cinemas, theaters, and entertainment venues', '2025-08-21 18:28:34.880008', '2025-08-21 18:28:34.880008') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.5ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Hotels' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.5ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Hotels') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.6ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Hotels', 'Hotels, motels, and accommodation services', '2025-08-21 18:28:34.902926', '2025-08-21 18:28:34.902926') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.5ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Services' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.4ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Services') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.4ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Services', 'Professional and personal services', '2025-08-21 18:28:34.918071', '2025-08-21 18:28:34.918071') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Health & Fitness' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.1ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Health & Fitness') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.2ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Health & Fitness', 'Gyms, spas, and health services', '2025-08-21 18:28:34.926981', '2025-08-21 18:28:34.926981') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Education' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (0.9ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Education') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.2ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Education', 'Schools, universities, and educational institutions', '2025-08-21 18:28:34.938116', '2025-08-21 18:28:34.938116') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Automotive' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.8ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Automotive') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.3ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Automotive', 'Car dealerships, repair shops, and automotive services', '2025-08-21 18:28:34.950118', '2025-08-21 18:28:34.950118') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (1.0ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mUser Exists? (0.5ms)[0m  [1m[34mSELECT 1 AS one FROM "users" WHERE LOWER("users"."email") = LOWER('<EMAIL>') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Create (1.6ms)[0m  [1m[32mINSERT INTO "users" ("email", "first_name", "last_name", "bio", "created_at", "updated_at", "password_digest") VALUES ('<EMAIL>', 'John', 'Doe', 'Food enthusiast and local explorer', '2025-08-21 18:28:35.331194', '2025-08-21 18:28:35.331194', '$2a$12$oVSoKT1mt.dq5uDCf/DvLexytGgESetf1Zgp2CF4z7oHvT85VVVmu') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.5ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mUser Exists? (1.6ms)[0m  [1m[34mSELECT 1 AS one FROM "users" WHERE LOWER("users"."email") = LOWER('<EMAIL>') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Create (0.3ms)[0m  [1m[32mINSERT INTO "users" ("email", "first_name", "last_name", "bio", "created_at", "updated_at", "password_digest") VALUES ('<EMAIL>', 'Jane', 'Smith', 'Travel blogger and restaurant critic', '2025-08-21 18:28:35.638585', '2025-08-21 18:28:35.638585', '$2a$12$YvDcjmlPDIRfikg4gY5znOXCM7qyJBq5q.qHbD055aGbZ2pAZ9BXK') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.7ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mUser Exists? (1.7ms)[0m  [1m[34mSELECT 1 AS one FROM "users" WHERE LOWER("users"."email") = LOWER('<EMAIL>') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Create (0.4ms)[0m  [1m[32mINSERT INTO "users" ("email", "first_name", "last_name", "bio", "created_at", "updated_at", "password_digest") VALUES ('<EMAIL>', 'Mike', 'Johnson', 'Local business owner', '2025-08-21 18:28:35.940023', '2025-08-21 18:28:35.940023', '$2a$12$uKK715ZCQG1xuq81AW6TL.0yzTEMNMjMMqdpvIPiF56ESpzkQSXU6') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.5ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Restaurants' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.8ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.8ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'The Gourmet Corner' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (2.4ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('The Gourmet Corner', 'Fine dining restaurant with contemporary cuisine and excellent wine selection.', '123 Main Street, Downtown', '(*************', 'https://gourmetcorner.com', '<EMAIL>', NULL, NULL, TRUE, 1, 1, '2025-08-21 18:28:36.000688', '2025-08-21 18:28:36.000688') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.5ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Shops' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Tech Haven Electronics' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (4.8ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('Tech Haven Electronics', 'Your one-stop shop for the latest electronics, gadgets, and tech accessories.', '456 Tech Avenue, Silicon Valley', '(*************', 'https://techhaven.com', '<EMAIL>', NULL, NULL, TRUE, 2, 1, '2025-08-21 18:28:36.013977', '2025-08-21 18:28:36.013977') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Entertainment' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.3ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Starlight Cinema' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (1.2ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('Starlight Cinema', 'Modern movie theater with IMAX screens and premium seating options.', '789 Entertainment Blvd, City Center', '(*************', 'https://starlightcinema.com', '<EMAIL>', NULL, NULL, TRUE, 3, 1, '2025-08-21 18:28:36.030483', '2025-08-21 18:28:36.030483') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Hotels' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.3ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Grand Plaza Hotel' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (1.6ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('Grand Plaza Hotel', 'Luxury hotel in the heart of the city with world-class amenities.', '321 Hotel Row, Business District', '(*************', 'https://grandplaza.com', '<EMAIL>', NULL, NULL, TRUE, 4, 1, '2025-08-21 18:28:36.040875', '2025-08-21 18:28:36.040875') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Health & Fitness' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'FitLife Gym' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (1.2ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('FitLife Gym', 'State-of-the-art fitness center with personal training and group classes.', '654 Fitness Street, Health District', '(*************', 'https://fitlifegym.com', '<EMAIL>', NULL, NULL, TRUE, 6, 1, '2025-08-21 18:28:36.051049', '2025-08-21 18:28:36.051049') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.4ms)[0m  [1m[34mSELECT "entities".* FROM "entities" /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.7ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 1 AND "reviews"."user_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.0ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 2 AND "reviews"."entity_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.9ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (4, 'Great experience! Highly recommended.', 2, 1, '2025-08-21 18:28:36.109485', '2025-08-21 18:28:36.109485') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 2 AND "reviews"."user_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.8ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 2 AND "reviews"."entity_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.2ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (3, 'Outstanding quality and friendly staff.', 2, 2, '2025-08-21 18:28:36.123241', '2025-08-21 18:28:36.123241') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.1ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.1ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 3 AND "reviews"."user_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.7ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 1 AND "reviews"."entity_id" = 3 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.2ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (3, 'Great experience! Highly recommended.', 1, 3, '2025-08-21 18:28:36.130641', '2025-08-21 18:28:36.130641') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.1ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 4 AND "reviews"."user_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.0ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 2 AND "reviews"."entity_id" = 4 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.6ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (5, 'Very satisfied with the service.', 2, 4, '2025-08-21 18:28:36.144200', '2025-08-21 18:28:36.144200') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 5 AND "reviews"."user_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.9ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 1 AND "reviews"."entity_id" = 5 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.3ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (3, 'Great experience! Highly recommended.', 1, 5, '2025-08-21 18:28:36.152768', '2025-08-21 18:28:36.152768') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.5ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 5 AND "reviews"."user_id" = 3 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.8ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 3 AND "reviews"."entity_id" = 5 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.3ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (5, 'Great experience! Highly recommended.', 3, 5, '2025-08-21 18:28:36.157440', '2025-08-21 18:28:36.157440') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.1ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 5 AND "reviews"."user_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.7ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 2 AND "reviews"."entity_id" = 5 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.2ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (3, 'Great experience! Highly recommended.', 2, 5, '2025-08-21 18:28:36.161943', '2025-08-21 18:28:36.161943') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "categories" /*application='CloudvisionApp'*/[0m
  [1m[36mUser Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" /*application='CloudvisionApp'*/[0m
Started HEAD "/" for ::1 at 2025-08-22 00:29:01 +0600
  [1m[36mActiveRecord::SchemaMigration Load (2.7ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
Processing by HomeController#index as */*
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (1.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.7ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (1.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.7ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.8ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.6ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (1.1ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.6ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.5ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.5ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.7ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.4ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.4ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (0.5ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (2, 3, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.4ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 304.9ms | GC: 0.0ms)
  Rendered layout layouts/application.html.erb (Duration: 324.0ms | GC: 0.0ms)
Completed 200 OK in 475ms (Views: 306.9ms | ActiveRecord: 29.3ms (47 queries, 20 cached) | GC: 1.6ms)


Started GET "/" for ::1 at 2025-08-22 00:29:13 +0600
Processing by HomeController#index as HTML
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (0.6ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.7ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.3ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.1ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.3ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (0.3ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (2, 3, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 66.9ms | GC: 0.0ms)
  Rendered layout layouts/application.html.erb (Duration: 72.0ms | GC: 0.0ms)
Completed 200 OK in 105ms (Views: 66.3ms | ActiveRecord: 6.5ms (47 queries, 20 cached) | GC: 0.0ms)


Started GET "/" for ::1 at 2025-08-22 00:29:14 +0600
Processing by HomeController#index as HTML
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (1.5ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (1.0ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (1.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.1ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.2ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (2, 3, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 66.8ms | GC: 27.0ms)
  Rendered layout layouts/application.html.erb (Duration: 73.2ms | GC: 27.0ms)
Completed 200 OK in 109ms (Views: 66.2ms | ActiveRecord: 7.3ms (47 queries, 20 cached) | GC: 27.0ms)


Started HEAD "/" for ::1 at 2025-08-22 00:34:47 +0600
  [1m[36mActiveRecord::SchemaMigration Load (1.4ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
Processing by HomeController#index as */*
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (2.1ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (1.8ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.8ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (1.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.7ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.8ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (1.7ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.7ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.5ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.5ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.4ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (3.5ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (2, 3, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.5ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 312.8ms | GC: 0.0ms)
  Rendered layout layouts/application.html.erb (Duration: 330.9ms | GC: 0.0ms)
Completed 200 OK in 485ms (Views: 302.1ms | ActiveRecord: 45.6ms (47 queries, 20 cached) | GC: 0.0ms)


Started GET "/" for ::1 at 2025-08-22 00:34:56 +0600
Processing by HomeController#index as HTML
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (4.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.8ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.3ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.7ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.8ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.7ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.2ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (2, 3, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 194.7ms | GC: 38.1ms)
  Rendered layout layouts/application.html.erb (Duration: 201.8ms | GC: 38.1ms)
Completed 200 OK in 251ms (Views: 194.1ms | ActiveRecord: 10.2ms (47 queries, 20 cached) | GC: 41.5ms)


Started GET "/assets/application-5d63fa00.css" for ::1 at 2025-08-22 00:34:56 +0600
Started GET "/categories/6" for ::1 at 2025-08-22 00:35:02 +0600
Processing by CategoriesController#show as HTML
  Parameters: {"id" => "6"}
  [1m[36mCategory Load (0.9ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" = 6 LIMIT 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/controllers/categories_controller.rb:15:in 'CategoriesController#set_category'
  Rendering layout layouts/application.html.erb
  Rendering categories/show.html.erb within layouts/application
  [1m[36mEntity Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 AND "entities"."active" = TRUE /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:31
  [1m[36mEntity Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "entities" WHERE "entities"."category_id" = 6 AND "entities"."active" = TRUE LIMIT 1 OFFSET 0 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:37
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" = 6 AND "entities"."active" = TRUE LIMIT 12 OFFSET 0 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:39
  [1m[36mReview Load (0.4ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:39
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  Rendered categories/show.html.erb within layouts/application (Duration: 31.3ms | GC: 0.1ms)
  Rendered layout layouts/application.html.erb (Duration: 31.8ms | GC: 0.1ms)
Completed 500 Internal Server Error in 78ms (ActiveRecord: 2.9ms (11 queries, 4 cached) | GC: 0.4ms)


  
ActionView::Template::Error (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
)
Caused by: ActionView::MissingTemplate (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
)

Information for: ActionView::Template::Error (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
):
    89:   </div>
    90: 
    91:   <!-- Pagination -->
    92:   <%= paginate @entities, theme: 'twitter-bootstrap-4' if respond_to?(:paginate) %>
    93: <% else %>
    94:   <div class="text-center py-12">
    95:     <div class="text-6xl mb-4">
  
app/views/categories/show.html.erb:92

Information for cause: ActionView::MissingTemplate (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
):
  
app/views/categories/show.html.erb:92
Started GET "/categories/1" for ::1 at 2025-08-22 00:35:02 +0600
Processing by CategoriesController#show as HTML
  Parameters: {"id" => "1"}
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" = 1 LIMIT 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/controllers/categories_controller.rb:15:in 'CategoriesController#set_category'
  Rendering layout layouts/application.html.erb
  Rendering categories/show.html.erb within layouts/application
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 AND "entities"."active" = TRUE /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:31
  [1m[36mEntity Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "entities" WHERE "entities"."category_id" = 1 AND "entities"."active" = TRUE LIMIT 1 OFFSET 0 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:37
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" = 1 AND "entities"."active" = TRUE LIMIT 12 OFFSET 0 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:39
  [1m[36mReview Load (0.3ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:39
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  Rendered categories/show.html.erb within layouts/application (Duration: 15.1ms | GC: 0.4ms)
  Rendered layout layouts/application.html.erb (Duration: 15.2ms | GC: 0.4ms)
Completed 500 Internal Server Error in 51ms (ActiveRecord: 2.1ms (11 queries, 4 cached) | GC: 0.4ms)


  
ActionView::Template::Error (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
)
Caused by: ActionView::MissingTemplate (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
)

Information for: ActionView::Template::Error (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
):
    89:   </div>
    90: 
    91:   <!-- Pagination -->
    92:   <%= paginate @entities, theme: 'twitter-bootstrap-4' if respond_to?(:paginate) %>
    93: <% else %>
    94:   <div class="text-center py-12">
    95:     <div class="text-6xl mb-4">
  
app/views/categories/show.html.erb:92

Information for cause: ActionView::MissingTemplate (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
):
  
app/views/categories/show.html.erb:92
Started GET "/categories/6" for ::1 at 2025-08-22 00:35:09 +0600
Processing by CategoriesController#show as HTML
  Parameters: {"id" => "6"}
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" = 6 LIMIT 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/controllers/categories_controller.rb:15:in 'CategoriesController#set_category'
  Rendering layout layouts/application.html.erb
  Rendering categories/show.html.erb within layouts/application
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 AND "entities"."active" = TRUE /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:31
  [1m[36mEntity Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "entities" WHERE "entities"."category_id" = 6 AND "entities"."active" = TRUE LIMIT 1 OFFSET 0 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:37
  [1m[36mEntity Load (0.1ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" = 6 AND "entities"."active" = TRUE LIMIT 12 OFFSET 0 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:39
  [1m[36mReview Load (0.1ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:39
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  Rendered categories/show.html.erb within layouts/application (Duration: 6.5ms | GC: 0.3ms)
  Rendered layout layouts/application.html.erb (Duration: 6.6ms | GC: 0.3ms)
Completed 500 Internal Server Error in 46ms (ActiveRecord: 1.1ms (11 queries, 4 cached) | GC: 0.3ms)


  
ActionView::Template::Error (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
)
Caused by: ActionView::MissingTemplate (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
)

Information for: ActionView::Template::Error (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
):
    89:   </div>
    90: 
    91:   <!-- Pagination -->
    92:   <%= paginate @entities, theme: 'twitter-bootstrap-4' if respond_to?(:paginate) %>
    93: <% else %>
    94:   <div class="text-center py-12">
    95:     <div class="text-6xl mb-4">
  
app/views/categories/show.html.erb:92

Information for cause: ActionView::MissingTemplate (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
):
  
app/views/categories/show.html.erb:92
Started GET "/categories/2" for ::1 at 2025-08-22 00:35:10 +0600
Processing by CategoriesController#show as HTML
  Parameters: {"id" => "2"}
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" = 2 LIMIT 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/controllers/categories_controller.rb:15:in 'CategoriesController#set_category'
  Rendering layout layouts/application.html.erb
  Rendering categories/show.html.erb within layouts/application
  [1m[36mEntity Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 AND "entities"."active" = TRUE /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:31
  [1m[36mEntity Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "entities" WHERE "entities"."category_id" = 2 AND "entities"."active" = TRUE LIMIT 1 OFFSET 0 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:37
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" = 2 AND "entities"."active" = TRUE LIMIT 12 OFFSET 0 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:39
  [1m[36mReview Load (0.2ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:39
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  Rendered categories/show.html.erb within layouts/application (Duration: 8.9ms | GC: 0.5ms)
  Rendered layout layouts/application.html.erb (Duration: 9.0ms | GC: 0.5ms)
Completed 500 Internal Server Error in 52ms (ActiveRecord: 2.0ms (11 queries, 4 cached) | GC: 1.1ms)


  
ActionView::Template::Error (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
)
Caused by: ActionView::MissingTemplate (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
)

Information for: ActionView::Template::Error (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
):
    89:   </div>
    90: 
    91:   <!-- Pagination -->
    92:   <%= paginate @entities, theme: 'twitter-bootstrap-4' if respond_to?(:paginate) %>
    93: <% else %>
    94:   <div class="text-center py-12">
    95:     <div class="text-6xl mb-4">
  
app/views/categories/show.html.erb:92

Information for cause: ActionView::MissingTemplate (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
):
  
app/views/categories/show.html.erb:92
Started GET "/categories/7" for ::1 at 2025-08-22 00:35:14 +0600
Processing by CategoriesController#show as HTML
  Parameters: {"id" => "7"}
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" = 7 LIMIT 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/controllers/categories_controller.rb:15:in 'CategoriesController#set_category'
  Rendering layout layouts/application.html.erb
  Rendering categories/show.html.erb within layouts/application
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 AND "entities"."active" = TRUE /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:31
  [1m[36mEntity Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "entities" WHERE "entities"."category_id" = 7 AND "entities"."active" = TRUE LIMIT 1 OFFSET 0 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:37
  Rendered categories/show.html.erb within layouts/application (Duration: 3.1ms | GC: 0.0ms)
  Rendered layout layouts/application.html.erb (Duration: 8.9ms | GC: 0.0ms)
Completed 200 OK in 44ms (Views: 8.6ms | ActiveRecord: 1.0ms (3 queries, 0 cached) | GC: 0.0ms)


  [1m[36mActiveRecord::SchemaMigration Load (1.4ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
Started GET "/categories/8" for ::1 at 2025-08-22 00:40:57 +0600
Processing by CategoriesController#show as HTML
  Parameters: {"id" => "8"}
  [1m[36mCategory Load (1.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" = 8 LIMIT 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/controllers/categories_controller.rb:15:in 'CategoriesController#set_category'
  Rendering layout layouts/application.html.erb
  Rendering categories/show.html.erb within layouts/application
  [1m[36mEntity Count (0.7ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 AND "entities"."active" = TRUE /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:31
  [1m[36mEntity Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "entities" WHERE "entities"."category_id" = 8 AND "entities"."active" = TRUE LIMIT 1 OFFSET 0 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:37
  Rendered categories/show.html.erb within layouts/application (Duration: 9.8ms | GC: 0.0ms)
  Rendered layout layouts/application.html.erb (Duration: 26.9ms | GC: 0.7ms)
Completed 200 OK in 167ms (Views: 30.4ms | ActiveRecord: 8.5ms (3 queries, 0 cached) | GC: 14.7ms)


Started GET "/assets/application-4d3dd77e.css" for ::1 at 2025-08-22 00:40:57 +0600
Started HEAD "/" for ::1 at 2025-08-22 00:43:39 +0600
  [1m[36mActiveRecord::SchemaMigration Load (1.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
Processing by HomeController#index as */*
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (2.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (2.0ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.9ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (1.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (1.0ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (2.0ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.7ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.5ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (1.0ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.5ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (1.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.5ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.3ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (1.1ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (2, 3, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.5ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 302.0ms | GC: 0.0ms)
  Rendered layout layouts/application.html.erb (Duration: 320.9ms | GC: 0.0ms)
Completed 200 OK in 477ms (Views: 291.3ms | ActiveRecord: 49.0ms (47 queries, 20 cached) | GC: 2.5ms)


Started GET "/" for ::1 at 2025-08-22 00:43:47 +0600
Processing by HomeController#index as HTML
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (0.7ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.6ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.5ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.4ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.2ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (2, 3, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 75.6ms | GC: 0.0ms)
  Rendered layout layouts/application.html.erb (Duration: 80.9ms | GC: 0.0ms)
Completed 200 OK in 125ms (Views: 74.9ms | ActiveRecord: 6.8ms (47 queries, 20 cached) | GC: 0.0ms)


Started GET "/assets/application-4d4d2112.css" for ::1 at 2025-08-22 00:43:47 +0600
Started HEAD "/google_places/search" for ::1 at 2025-08-22 00:43:58 +0600
Processing by GooglePlacesController#search as */*
Redirected to http://localhost:3000/sign_in
Filter chain halted as :authenticate_user! rendered or redirected
Completed 302 Found in 48ms (ActiveRecord: 0.0ms (0 queries, 0 cached) | GC: 0.0ms)


Started GET "/categories/4" for ::1 at 2025-08-22 01:29:25 +0600
Processing by CategoriesController#show as HTML
  Parameters: {"id" => "4"}
  [1m[36mCategory Load (13.8ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" = 4 LIMIT 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/controllers/categories_controller.rb:15:in 'CategoriesController#set_category'
  Rendering layout layouts/application.html.erb
  Rendering categories/show.html.erb within layouts/application
  [1m[36mEntity Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 AND "entities"."active" = TRUE /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:31
  [1m[36mEntity Exists? (0.1ms)[0m  [1m[34mSELECT 1 AS one FROM "entities" WHERE "entities"."category_id" = 4 AND "entities"."active" = TRUE LIMIT 1 OFFSET 0 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:37
  [1m[36mEntity Load (0.1ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" = 4 AND "entities"."active" = TRUE LIMIT 12 OFFSET 0 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:39
  [1m[36mReview Load (1.1ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:39
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  Rendered categories/show.html.erb within layouts/application (Duration: 34.8ms | GC: 0.8ms)
  Rendered layout layouts/application.html.erb (Duration: 35.1ms | GC: 0.8ms)
Completed 500 Internal Server Error in 101ms (ActiveRecord: 20.8ms (11 queries, 4 cached) | GC: 3.9ms)


  
ActionView::Template::Error (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
)
Caused by: ActionView::MissingTemplate (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
)

Information for: ActionView::Template::Error (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
):
    89:   </div>
    90: 
    91:   <!-- Pagination -->
    92:   <%= paginate @entities, theme: 'twitter-bootstrap-4' if respond_to?(:paginate) %>
    93: <% else %>
    94:   <div class="text-center py-12">
    95:     <div class="text-6xl mb-4">
  
app/views/categories/show.html.erb:92

Information for cause: ActionView::MissingTemplate (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
):
  
app/views/categories/show.html.erb:92
Started GET "/" for ::1 at 2025-08-22 03:05:18 +0600
Processing by HomeController#index as HTML
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (21.7ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (1.4ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.9ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.5ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (2.2ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.1ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.4ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (1.3ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (2, 3, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.4ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 103.5ms | GC: 40.7ms)
  Rendered layout layouts/application.html.erb (Duration: 115.2ms | GC: 44.5ms)
Completed 200 OK in 152ms (Views: 73.9ms | ActiveRecord: 42.4ms (47 queries, 20 cached) | GC: 44.5ms)


Started GET "/categories/7" for ::1 at 2025-08-22 03:05:19 +0600
Processing by CategoriesController#show as HTML
  Parameters: {"id" => "7"}
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" = 7 LIMIT 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/controllers/categories_controller.rb:15:in 'CategoriesController#set_category'
  Rendering layout layouts/application.html.erb
  Rendering categories/show.html.erb within layouts/application
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 AND "entities"."active" = TRUE /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:31
  [1m[36mEntity Exists? (0.1ms)[0m  [1m[34mSELECT 1 AS one FROM "entities" WHERE "entities"."category_id" = 7 AND "entities"."active" = TRUE LIMIT 1 OFFSET 0 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:37
  Rendered categories/show.html.erb within layouts/application (Duration: 4.2ms | GC: 1.8ms)
  Rendered layout layouts/application.html.erb (Duration: 6.4ms | GC: 1.8ms)
Completed 200 OK in 53ms (Views: 6.9ms | ActiveRecord: 0.7ms (3 queries, 0 cached) | GC: 1.8ms)


Started GET "/search?q=Thimphu&commit=Search" for ::1 at 2025-08-22 03:05:28 +0600
Processing by EntitiesController#search as HTML
  Parameters: {"q" => "Thimphu", "commit" => "Search"}
Completed 404 Not Found in 31ms (ActiveRecord: 0.0ms (0 queries, 0 cached) | GC: 0.0ms)


  
AbstractController::ActionNotFound (The reviews action could not be found for the :set_entity
callback on EntitiesController, but it is listed in the controller's
:only option.

Raising for missing callback actions is a new default in Rails 7.1, if you'd
like to turn this off you can delete the option from the environment configurations
or set `config.action_controller.raise_on_missing_callback_actions` to `false`.
):
  
actionpack (*******) lib/abstract_controller/callbacks.rb:62:in 'AbstractController::Callbacks::ActionFilter#match?'
activesupport (*******) lib/active_support/callbacks.rb:384:in 'block in ActiveSupport::Callbacks::CallTemplate::ObjectCall#make_lambda'
activesupport (*******) lib/active_support/callbacks.rb:177:in 'block in ActiveSupport::Callbacks::Filters::Before#call'
activesupport (*******) lib/active_support/callbacks.rb:177:in 'Array#all?'
activesupport (*******) lib/active_support/callbacks.rb:177:in 'ActiveSupport::Callbacks::Filters::Before#call'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'block in ActiveSupport::Callbacks::CallbackSequence#invoke_before'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'Array#each'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'ActiveSupport::Callbacks::CallbackSequence#invoke_before'
activesupport (*******) lib/active_support/callbacks.rb:118:in 'block in ActiveSupport::Callbacks#run_callbacks'
turbo-rails (2.0.16) lib/turbo-rails.rb:24:in 'Turbo.with_request_id'
turbo-rails (2.0.16) app/controllers/concerns/turbo/request_id_tracking.rb:10:in 'Turbo::RequestIdTracking#turbo_tracking_request_id'
activesupport (*******) lib/active_support/callbacks.rb:129:in 'block in ActiveSupport::Callbacks#run_callbacks'
actiontext (*******) lib/action_text/rendering.rb:25:in 'ActionText::Rendering::ClassMethods#with_renderer'
actiontext (*******) lib/action_text/engine.rb:71:in 'block (4 levels) in <class:Engine>'
activesupport (*******) lib/active_support/callbacks.rb:129:in 'BasicObject#instance_exec'
activesupport (*******) lib/active_support/callbacks.rb:129:in 'block in ActiveSupport::Callbacks#run_callbacks'
activesupport (*******) lib/active_support/callbacks.rb:140:in 'ActiveSupport::Callbacks#run_callbacks'
actionpack (*******) lib/abstract_controller/callbacks.rb:260:in 'AbstractController::Callbacks#process_action'
actionpack (*******) lib/action_controller/metal/rescue.rb:27:in 'ActionController::Rescue#process_action'
actionpack (*******) lib/action_controller/metal/instrumentation.rb:76:in 'block in ActionController::Instrumentation#process_action'
activesupport (*******) lib/active_support/notifications.rb:210:in 'block in ActiveSupport::Notifications.instrument'
activesupport (*******) lib/active_support/notifications/instrumenter.rb:58:in 'ActiveSupport::Notifications::Instrumenter#instrument'
activesupport (*******) lib/active_support/notifications.rb:210:in 'ActiveSupport::Notifications.instrument'
actionpack (*******) lib/action_controller/metal/instrumentation.rb:75:in 'ActionController::Instrumentation#process_action'
actionpack (*******) lib/action_controller/metal/params_wrapper.rb:259:in 'ActionController::ParamsWrapper#process_action'
activerecord (*******) lib/active_record/railties/controller_runtime.rb:39:in 'ActiveRecord::Railties::ControllerRuntime#process_action'
actionpack (*******) lib/abstract_controller/base.rb:163:in 'AbstractController::Base#process'
actionview (*******) lib/action_view/rendering.rb:40:in 'ActionView::Rendering#process'
actionpack (*******) lib/action_controller/metal.rb:252:in 'ActionController::Metal#dispatch'
actionpack (*******) lib/action_controller/metal.rb:335:in 'ActionController::Metal.dispatch'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:67:in 'ActionDispatch::Routing::RouteSet::Dispatcher#dispatch'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:50:in 'ActionDispatch::Routing::RouteSet::Dispatcher#serve'
actionpack (*******) lib/action_dispatch/journey/router.rb:53:in 'block in ActionDispatch::Journey::Router#serve'
actionpack (*******) lib/action_dispatch/journey/router.rb:133:in 'block in ActionDispatch::Journey::Router#find_routes'
actionpack (*******) lib/action_dispatch/journey/router.rb:126:in 'Array#each'
actionpack (*******) lib/action_dispatch/journey/router.rb:126:in 'ActionDispatch::Journey::Router#find_routes'
actionpack (*******) lib/action_dispatch/journey/router.rb:34:in 'ActionDispatch::Journey::Router#serve'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:908:in 'ActionDispatch::Routing::RouteSet#call'
railties (*******) lib/rails/engine/lazy_route_set.rb:68:in 'Rails::Engine::LazyRouteSet#call'
rack (3.2.0) lib/rack/tempfile_reaper.rb:20:in 'Rack::TempfileReaper#call'
rack (3.2.0) lib/rack/etag.rb:29:in 'Rack::ETag#call'
rack (3.2.0) lib/rack/conditional_get.rb:31:in 'Rack::ConditionalGet#call'
rack (3.2.0) lib/rack/head.rb:15:in 'Rack::Head#call'
actionpack (*******) lib/action_dispatch/http/permissions_policy.rb:38:in 'ActionDispatch::PermissionsPolicy::Middleware#call'
actionpack (*******) lib/action_dispatch/http/content_security_policy.rb:38:in 'ActionDispatch::ContentSecurityPolicy::Middleware#call'
rack-session (2.1.1) lib/rack/session/abstract/id.rb:274:in 'Rack::Session::Abstract::Persisted#context'
rack-session (2.1.1) lib/rack/session/abstract/id.rb:268:in 'Rack::Session::Abstract::Persisted#call'
actionpack (*******) lib/action_dispatch/middleware/cookies.rb:706:in 'ActionDispatch::Cookies#call'
activerecord (*******) lib/active_record/migration.rb:671:in 'ActiveRecord::Migration::CheckPending#call'
actionpack (*******) lib/action_dispatch/middleware/callbacks.rb:31:in 'block in ActionDispatch::Callbacks#call'
activesupport (*******) lib/active_support/callbacks.rb:100:in 'ActiveSupport::Callbacks#run_callbacks'
actionpack (*******) lib/action_dispatch/middleware/callbacks.rb:30:in 'ActionDispatch::Callbacks#call'
actionpack (*******) lib/action_dispatch/middleware/executor.rb:16:in 'ActionDispatch::Executor#call'
actionpack (*******) lib/action_dispatch/middleware/actionable_exceptions.rb:18:in 'ActionDispatch::ActionableExceptions#call'
actionpack (*******) lib/action_dispatch/middleware/debug_exceptions.rb:31:in 'ActionDispatch::DebugExceptions#call'
web-console (4.2.1) lib/web_console/middleware.rb:132:in 'WebConsole::Middleware#call_app'
web-console (4.2.1) lib/web_console/middleware.rb:28:in 'block in WebConsole::Middleware#call'
web-console (4.2.1) lib/web_console/middleware.rb:17:in 'Kernel#catch'
web-console (4.2.1) lib/web_console/middleware.rb:17:in 'WebConsole::Middleware#call'
actionpack (*******) lib/action_dispatch/middleware/show_exceptions.rb:32:in 'ActionDispatch::ShowExceptions#call'
railties (*******) lib/rails/rack/logger.rb:41:in 'Rails::Rack::Logger#call_app'
railties (*******) lib/rails/rack/logger.rb:29:in 'Rails::Rack::Logger#call'
actionpack (*******) lib/action_dispatch/middleware/remote_ip.rb:96:in 'ActionDispatch::RemoteIp#call'
actionpack (*******) lib/action_dispatch/middleware/request_id.rb:34:in 'ActionDispatch::RequestId#call'
rack (3.2.0) lib/rack/method_override.rb:28:in 'Rack::MethodOverride#call'
rack (3.2.0) lib/rack/runtime.rb:24:in 'Rack::Runtime#call'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:61:in 'block in ActionDispatch::ServerTiming#call'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:26:in 'ActionDispatch::ServerTiming::Subscriber#collect_events'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:60:in 'ActionDispatch::ServerTiming#call'
actionpack (*******) lib/action_dispatch/middleware/executor.rb:16:in 'ActionDispatch::Executor#call'
actionpack (*******) lib/action_dispatch/middleware/static.rb:27:in 'ActionDispatch::Static#call'
rack (3.2.0) lib/rack/sendfile.rb:114:in 'Rack::Sendfile#call'
actionpack (*******) lib/action_dispatch/middleware/host_authorization.rb:143:in 'ActionDispatch::HostAuthorization#call'
railties (*******) lib/rails/engine.rb:535:in 'Rails::Engine#call'
puma (6.6.1) lib/puma/configuration.rb:279:in 'Puma::Configuration::ConfigMiddleware#call'
puma (6.6.1) lib/puma/request.rb:99:in 'block in Puma::Request#handle_request'
puma (6.6.1) lib/puma/thread_pool.rb:390:in 'Puma::ThreadPool#with_force_shutdown'
puma (6.6.1) lib/puma/request.rb:98:in 'Puma::Request#handle_request'
puma (6.6.1) lib/puma/server.rb:472:in 'Puma::Server#process_client'
puma (6.6.1) lib/puma/server.rb:254:in 'block in Puma::Server#run'
puma (6.6.1) lib/puma/thread_pool.rb:167:in 'block in Puma::ThreadPool#spawn_thread'
Started GET "/" for ::1 at 2025-08-22 03:05:28 +0600
Processing by HomeController#index as HTML
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.2ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.1ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.1ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (0.3ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (2, 3, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.1ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 30.7ms | GC: 3.8ms)
  Rendered layout layouts/application.html.erb (Duration: 34.8ms | GC: 3.8ms)
Completed 200 OK in 71ms (Views: 30.8ms | ActiveRecord: 4.4ms (47 queries, 20 cached) | GC: 3.8ms)


Started GET "/search?q=&commit=Search" for ::1 at 2025-08-22 03:05:30 +0600
Processing by EntitiesController#search as HTML
  Parameters: {"q" => "", "commit" => "Search"}
Completed 404 Not Found in 34ms (ActiveRecord: 0.0ms (0 queries, 0 cached) | GC: 0.0ms)


  
AbstractController::ActionNotFound (The reviews action could not be found for the :set_entity
callback on EntitiesController, but it is listed in the controller's
:only option.

Raising for missing callback actions is a new default in Rails 7.1, if you'd
like to turn this off you can delete the option from the environment configurations
or set `config.action_controller.raise_on_missing_callback_actions` to `false`.
):
  
actionpack (*******) lib/abstract_controller/callbacks.rb:62:in 'AbstractController::Callbacks::ActionFilter#match?'
activesupport (*******) lib/active_support/callbacks.rb:384:in 'block in ActiveSupport::Callbacks::CallTemplate::ObjectCall#make_lambda'
activesupport (*******) lib/active_support/callbacks.rb:177:in 'block in ActiveSupport::Callbacks::Filters::Before#call'
activesupport (*******) lib/active_support/callbacks.rb:177:in 'Array#all?'
activesupport (*******) lib/active_support/callbacks.rb:177:in 'ActiveSupport::Callbacks::Filters::Before#call'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'block in ActiveSupport::Callbacks::CallbackSequence#invoke_before'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'Array#each'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'ActiveSupport::Callbacks::CallbackSequence#invoke_before'
activesupport (*******) lib/active_support/callbacks.rb:118:in 'block in ActiveSupport::Callbacks#run_callbacks'
turbo-rails (2.0.16) lib/turbo-rails.rb:24:in 'Turbo.with_request_id'
turbo-rails (2.0.16) app/controllers/concerns/turbo/request_id_tracking.rb:10:in 'Turbo::RequestIdTracking#turbo_tracking_request_id'
activesupport (*******) lib/active_support/callbacks.rb:129:in 'block in ActiveSupport::Callbacks#run_callbacks'
actiontext (*******) lib/action_text/rendering.rb:25:in 'ActionText::Rendering::ClassMethods#with_renderer'
actiontext (*******) lib/action_text/engine.rb:71:in 'block (4 levels) in <class:Engine>'
activesupport (*******) lib/active_support/callbacks.rb:129:in 'BasicObject#instance_exec'
activesupport (*******) lib/active_support/callbacks.rb:129:in 'block in ActiveSupport::Callbacks#run_callbacks'
activesupport (*******) lib/active_support/callbacks.rb:140:in 'ActiveSupport::Callbacks#run_callbacks'
actionpack (*******) lib/abstract_controller/callbacks.rb:260:in 'AbstractController::Callbacks#process_action'
actionpack (*******) lib/action_controller/metal/rescue.rb:27:in 'ActionController::Rescue#process_action'
actionpack (*******) lib/action_controller/metal/instrumentation.rb:76:in 'block in ActionController::Instrumentation#process_action'
activesupport (*******) lib/active_support/notifications.rb:210:in 'block in ActiveSupport::Notifications.instrument'
activesupport (*******) lib/active_support/notifications/instrumenter.rb:58:in 'ActiveSupport::Notifications::Instrumenter#instrument'
activesupport (*******) lib/active_support/notifications.rb:210:in 'ActiveSupport::Notifications.instrument'
actionpack (*******) lib/action_controller/metal/instrumentation.rb:75:in 'ActionController::Instrumentation#process_action'
actionpack (*******) lib/action_controller/metal/params_wrapper.rb:259:in 'ActionController::ParamsWrapper#process_action'
activerecord (*******) lib/active_record/railties/controller_runtime.rb:39:in 'ActiveRecord::Railties::ControllerRuntime#process_action'
actionpack (*******) lib/abstract_controller/base.rb:163:in 'AbstractController::Base#process'
actionview (*******) lib/action_view/rendering.rb:40:in 'ActionView::Rendering#process'
actionpack (*******) lib/action_controller/metal.rb:252:in 'ActionController::Metal#dispatch'
actionpack (*******) lib/action_controller/metal.rb:335:in 'ActionController::Metal.dispatch'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:67:in 'ActionDispatch::Routing::RouteSet::Dispatcher#dispatch'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:50:in 'ActionDispatch::Routing::RouteSet::Dispatcher#serve'
actionpack (*******) lib/action_dispatch/journey/router.rb:53:in 'block in ActionDispatch::Journey::Router#serve'
actionpack (*******) lib/action_dispatch/journey/router.rb:133:in 'block in ActionDispatch::Journey::Router#find_routes'
actionpack (*******) lib/action_dispatch/journey/router.rb:126:in 'Array#each'
actionpack (*******) lib/action_dispatch/journey/router.rb:126:in 'ActionDispatch::Journey::Router#find_routes'
actionpack (*******) lib/action_dispatch/journey/router.rb:34:in 'ActionDispatch::Journey::Router#serve'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:908:in 'ActionDispatch::Routing::RouteSet#call'
railties (*******) lib/rails/engine/lazy_route_set.rb:68:in 'Rails::Engine::LazyRouteSet#call'
rack (3.2.0) lib/rack/tempfile_reaper.rb:20:in 'Rack::TempfileReaper#call'
rack (3.2.0) lib/rack/etag.rb:29:in 'Rack::ETag#call'
rack (3.2.0) lib/rack/conditional_get.rb:31:in 'Rack::ConditionalGet#call'
rack (3.2.0) lib/rack/head.rb:15:in 'Rack::Head#call'
actionpack (*******) lib/action_dispatch/http/permissions_policy.rb:38:in 'ActionDispatch::PermissionsPolicy::Middleware#call'
actionpack (*******) lib/action_dispatch/http/content_security_policy.rb:38:in 'ActionDispatch::ContentSecurityPolicy::Middleware#call'
rack-session (2.1.1) lib/rack/session/abstract/id.rb:274:in 'Rack::Session::Abstract::Persisted#context'
rack-session (2.1.1) lib/rack/session/abstract/id.rb:268:in 'Rack::Session::Abstract::Persisted#call'
actionpack (*******) lib/action_dispatch/middleware/cookies.rb:706:in 'ActionDispatch::Cookies#call'
activerecord (*******) lib/active_record/migration.rb:671:in 'ActiveRecord::Migration::CheckPending#call'
actionpack (*******) lib/action_dispatch/middleware/callbacks.rb:31:in 'block in ActionDispatch::Callbacks#call'
activesupport (*******) lib/active_support/callbacks.rb:100:in 'ActiveSupport::Callbacks#run_callbacks'
actionpack (*******) lib/action_dispatch/middleware/callbacks.rb:30:in 'ActionDispatch::Callbacks#call'
actionpack (*******) lib/action_dispatch/middleware/executor.rb:16:in 'ActionDispatch::Executor#call'
actionpack (*******) lib/action_dispatch/middleware/actionable_exceptions.rb:18:in 'ActionDispatch::ActionableExceptions#call'
actionpack (*******) lib/action_dispatch/middleware/debug_exceptions.rb:31:in 'ActionDispatch::DebugExceptions#call'
web-console (4.2.1) lib/web_console/middleware.rb:132:in 'WebConsole::Middleware#call_app'
web-console (4.2.1) lib/web_console/middleware.rb:28:in 'block in WebConsole::Middleware#call'
web-console (4.2.1) lib/web_console/middleware.rb:17:in 'Kernel#catch'
web-console (4.2.1) lib/web_console/middleware.rb:17:in 'WebConsole::Middleware#call'
actionpack (*******) lib/action_dispatch/middleware/show_exceptions.rb:32:in 'ActionDispatch::ShowExceptions#call'
railties (*******) lib/rails/rack/logger.rb:41:in 'Rails::Rack::Logger#call_app'
railties (*******) lib/rails/rack/logger.rb:29:in 'Rails::Rack::Logger#call'
actionpack (*******) lib/action_dispatch/middleware/remote_ip.rb:96:in 'ActionDispatch::RemoteIp#call'
actionpack (*******) lib/action_dispatch/middleware/request_id.rb:34:in 'ActionDispatch::RequestId#call'
rack (3.2.0) lib/rack/method_override.rb:28:in 'Rack::MethodOverride#call'
rack (3.2.0) lib/rack/runtime.rb:24:in 'Rack::Runtime#call'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:61:in 'block in ActionDispatch::ServerTiming#call'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:26:in 'ActionDispatch::ServerTiming::Subscriber#collect_events'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:60:in 'ActionDispatch::ServerTiming#call'
actionpack (*******) lib/action_dispatch/middleware/executor.rb:16:in 'ActionDispatch::Executor#call'
actionpack (*******) lib/action_dispatch/middleware/static.rb:27:in 'ActionDispatch::Static#call'
rack (3.2.0) lib/rack/sendfile.rb:114:in 'Rack::Sendfile#call'
actionpack (*******) lib/action_dispatch/middleware/host_authorization.rb:143:in 'ActionDispatch::HostAuthorization#call'
railties (*******) lib/rails/engine.rb:535:in 'Rails::Engine#call'
puma (6.6.1) lib/puma/configuration.rb:279:in 'Puma::Configuration::ConfigMiddleware#call'
puma (6.6.1) lib/puma/request.rb:99:in 'block in Puma::Request#handle_request'
puma (6.6.1) lib/puma/thread_pool.rb:390:in 'Puma::ThreadPool#with_force_shutdown'
puma (6.6.1) lib/puma/request.rb:98:in 'Puma::Request#handle_request'
puma (6.6.1) lib/puma/server.rb:472:in 'Puma::Server#process_client'
puma (6.6.1) lib/puma/server.rb:254:in 'block in Puma::Server#run'
puma (6.6.1) lib/puma/thread_pool.rb:167:in 'block in Puma::ThreadPool#spawn_thread'
Started GET "/" for ::1 at 2025-08-22 03:05:30 +0600
Processing by HomeController#index as HTML
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.1ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.2ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.3ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (0.3ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (2, 3, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.1ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 28.1ms | GC: 3.8ms)
  Rendered layout layouts/application.html.erb (Duration: 31.3ms | GC: 3.8ms)
Completed 200 OK in 65ms (Views: 27.9ms | ActiveRecord: 4.4ms (47 queries, 20 cached) | GC: 3.8ms)


Started GET "/categories/8" for ::1 at 2025-08-22 03:05:30 +0600
Processing by CategoriesController#show as HTML
  Parameters: {"id" => "8"}
  [1m[36mCategory Load (0.6ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" = 8 LIMIT 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/controllers/categories_controller.rb:15:in 'CategoriesController#set_category'
  Rendering layout layouts/application.html.erb
  Rendering categories/show.html.erb within layouts/application
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 AND "entities"."active" = TRUE /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:31
  [1m[36mEntity Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "entities" WHERE "entities"."category_id" = 8 AND "entities"."active" = TRUE LIMIT 1 OFFSET 0 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:37
  Rendered categories/show.html.erb within layouts/application (Duration: 7.9ms | GC: 0.9ms)
  Rendered layout layouts/application.html.erb (Duration: 10.2ms | GC: 0.9ms)
Completed 200 OK in 53ms (Views: 10.6ms | ActiveRecord: 1.3ms (3 queries, 0 cached) | GC: 0.9ms)


Started GET "/sign_in" for ::1 at 2025-08-22 03:05:32 +0600
Processing by SessionsController#new as HTML
  Rendering layout layouts/application.html.erb
  Rendering sessions/new.html.erb within layouts/application
  Rendered sessions/new.html.erb within layouts/application (Duration: 10.9ms | GC: 0.0ms)
  Rendered layout layouts/application.html.erb (Duration: 12.4ms | GC: 0.0ms)
Completed 200 OK in 55ms (Views: 14.2ms | ActiveRecord: 0.0ms (0 queries, 0 cached) | GC: 0.4ms)


Started GET "/sign_up" for ::1 at 2025-08-22 03:05:33 +0600
Processing by RegistrationsController#new as HTML
  Rendering layout layouts/application.html.erb
  Rendering registrations/new.html.erb within layouts/application
  Rendered registrations/new.html.erb within layouts/application (Duration: 18.7ms | GC: 0.4ms)
  Rendered layout layouts/application.html.erb (Duration: 19.9ms | GC: 0.4ms)
Completed 200 OK in 58ms (Views: 20.8ms | ActiveRecord: 0.0ms (0 queries, 0 cached) | GC: 0.4ms)


Started POST "/sign_up" for ::1 at 2025-08-22 03:06:57 +0600
Processing by RegistrationsController#create as TURBO_STREAM
  Parameters: {"authenticity_token" => "[FILTERED]", "user" => {"first_name" => "Tashi", "last_name" => "Dendup", "email" => "[FILTERED]", "password" => "[FILTERED]", "password_confirmation" => "[FILTERED]", "bio" => "Hello I am who I am."}, "commit" => "Sign Up"}
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*action='create',application='CloudvisionApp',controller='registrations'*/[0m
  ↳ app/controllers/registrations_controller.rb:10:in 'RegistrationsController#create'
  [1m[36mUser Exists? (1.8ms)[0m  [1m[34mSELECT 1 AS one FROM "users" WHERE LOWER("users"."email") = LOWER('<EMAIL>') LIMIT 1 /*action='create',application='CloudvisionApp',controller='registrations'*/[0m
  ↳ app/controllers/registrations_controller.rb:10:in 'RegistrationsController#create'
  [1m[36mUser Create (0.9ms)[0m  [1m[32mINSERT INTO "users" ("email", "first_name", "last_name", "bio", "created_at", "updated_at", "password_digest") VALUES ('<EMAIL>', 'Tashi', 'Dendup', 'Hello I am who I am.', '2025-08-21 21:06:58.026468', '2025-08-21 21:06:58.026468', '$2a$12$Y4C89cw79L88fOoscXN/turkLMyO9aD9OfV.VRCU.sBoE6iZzhiqm') RETURNING "id" /*action='create',application='CloudvisionApp',controller='registrations'*/[0m
  ↳ app/controllers/registrations_controller.rb:10:in 'RegistrationsController#create'
  [1m[36mTRANSACTION (2.1ms)[0m  [1m[35mCOMMIT /*action='create',application='CloudvisionApp',controller='registrations'*/[0m
  ↳ app/controllers/registrations_controller.rb:10:in 'RegistrationsController#create'
Redirected to http://localhost:3000/
Completed 302 Found in 364ms (ActiveRecord: 9.8ms (2 queries, 0 cached) | GC: 1.3ms)


Started GET "/" for ::1 at 2025-08-22 03:06:58 +0600
Processing by HomeController#index as TURBO_STREAM
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (0.7ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.5ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.4ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.5ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.5ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.3ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (0.5ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (2, 3, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.4ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 94.3ms | GC: 37.7ms)
  [1m[36mUser Load (0.4ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = 4 LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/controllers/application_controller.rb:8:in 'ApplicationController#current_user'
  Rendered layout layouts/application.html.erb (Duration: 105.3ms | GC: 41.2ms)
Completed 200 OK in 137ms (Views: 97.3ms | ActiveRecord: 8.6ms (48 queries, 20 cached) | GC: 41.2ms)


Started GET "/categories/3" for ::1 at 2025-08-22 03:06:58 +0600
Processing by CategoriesController#show as HTML
  Parameters: {"id" => "3"}
  [1m[36mCategory Load (0.6ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" = 3 LIMIT 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/controllers/categories_controller.rb:15:in 'CategoriesController#set_category'
  Rendering layout layouts/application.html.erb
  Rendering categories/show.html.erb within layouts/application
  [1m[36mEntity Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 AND "entities"."active" = TRUE /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:31
  [1m[36mEntity Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "entities" WHERE "entities"."category_id" = 3 AND "entities"."active" = TRUE LIMIT 1 OFFSET 0 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:37
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" = 3 AND "entities"."active" = TRUE LIMIT 12 OFFSET 0 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:39
  [1m[36mReview Load (0.3ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:39
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  Rendered categories/show.html.erb within layouts/application (Duration: 14.8ms | GC: 2.2ms)
  Rendered layout layouts/application.html.erb (Duration: 15.2ms | GC: 2.3ms)
Completed 500 Internal Server Error in 51ms (ActiveRecord: 2.4ms (11 queries, 4 cached) | GC: 3.9ms)


  
ActionView::Template::Error (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
)
Caused by: ActionView::MissingTemplate (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
)

Information for: ActionView::Template::Error (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
):
    89:   </div>
    90: 
    91:   <!-- Pagination -->
    92:   <%= paginate @entities, theme: 'twitter-bootstrap-4' if respond_to?(:paginate) %>
    93: <% else %>
    94:   <div class="text-center py-12">
    95:     <div class="text-6xl mb-4">
  
app/views/categories/show.html.erb:92

Information for cause: ActionView::MissingTemplate (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
):
  
app/views/categories/show.html.erb:92
Started GET "/entities/new" for ::1 at 2025-08-22 03:07:01 +0600
Processing by EntitiesController#new as HTML
  [1m[36mUser Load (1.1ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = 4 LIMIT 1 /*action='new',application='CloudvisionApp',controller='entities'*/[0m
  ↳ app/controllers/application_controller.rb:8:in 'ApplicationController#current_user'
Completed 404 Not Found in 41ms (ActiveRecord: 1.1ms (1 query, 0 cached) | GC: 0.0ms)


  
AbstractController::ActionNotFound (The reviews action could not be found for the :set_entity
callback on EntitiesController, but it is listed in the controller's
:only option.

Raising for missing callback actions is a new default in Rails 7.1, if you'd
like to turn this off you can delete the option from the environment configurations
or set `config.action_controller.raise_on_missing_callback_actions` to `false`.
):
  
actionpack (*******) lib/abstract_controller/callbacks.rb:62:in 'AbstractController::Callbacks::ActionFilter#match?'
activesupport (*******) lib/active_support/callbacks.rb:384:in 'block in ActiveSupport::Callbacks::CallTemplate::ObjectCall#make_lambda'
activesupport (*******) lib/active_support/callbacks.rb:177:in 'block in ActiveSupport::Callbacks::Filters::Before#call'
activesupport (*******) lib/active_support/callbacks.rb:177:in 'Array#all?'
activesupport (*******) lib/active_support/callbacks.rb:177:in 'ActiveSupport::Callbacks::Filters::Before#call'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'block in ActiveSupport::Callbacks::CallbackSequence#invoke_before'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'Array#each'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'ActiveSupport::Callbacks::CallbackSequence#invoke_before'
activesupport (*******) lib/active_support/callbacks.rb:118:in 'block in ActiveSupport::Callbacks#run_callbacks'
turbo-rails (2.0.16) lib/turbo-rails.rb:24:in 'Turbo.with_request_id'
turbo-rails (2.0.16) app/controllers/concerns/turbo/request_id_tracking.rb:10:in 'Turbo::RequestIdTracking#turbo_tracking_request_id'
activesupport (*******) lib/active_support/callbacks.rb:129:in 'block in ActiveSupport::Callbacks#run_callbacks'
actiontext (*******) lib/action_text/rendering.rb:25:in 'ActionText::Rendering::ClassMethods#with_renderer'
actiontext (*******) lib/action_text/engine.rb:71:in 'block (4 levels) in <class:Engine>'
activesupport (*******) lib/active_support/callbacks.rb:129:in 'BasicObject#instance_exec'
activesupport (*******) lib/active_support/callbacks.rb:129:in 'block in ActiveSupport::Callbacks#run_callbacks'
activesupport (*******) lib/active_support/callbacks.rb:140:in 'ActiveSupport::Callbacks#run_callbacks'
actionpack (*******) lib/abstract_controller/callbacks.rb:260:in 'AbstractController::Callbacks#process_action'
actionpack (*******) lib/action_controller/metal/rescue.rb:27:in 'ActionController::Rescue#process_action'
actionpack (*******) lib/action_controller/metal/instrumentation.rb:76:in 'block in ActionController::Instrumentation#process_action'
activesupport (*******) lib/active_support/notifications.rb:210:in 'block in ActiveSupport::Notifications.instrument'
activesupport (*******) lib/active_support/notifications/instrumenter.rb:58:in 'ActiveSupport::Notifications::Instrumenter#instrument'
activesupport (*******) lib/active_support/notifications.rb:210:in 'ActiveSupport::Notifications.instrument'
actionpack (*******) lib/action_controller/metal/instrumentation.rb:75:in 'ActionController::Instrumentation#process_action'
actionpack (*******) lib/action_controller/metal/params_wrapper.rb:259:in 'ActionController::ParamsWrapper#process_action'
activerecord (*******) lib/active_record/railties/controller_runtime.rb:39:in 'ActiveRecord::Railties::ControllerRuntime#process_action'
actionpack (*******) lib/abstract_controller/base.rb:163:in 'AbstractController::Base#process'
actionview (*******) lib/action_view/rendering.rb:40:in 'ActionView::Rendering#process'
actionpack (*******) lib/action_controller/metal.rb:252:in 'ActionController::Metal#dispatch'
actionpack (*******) lib/action_controller/metal.rb:335:in 'ActionController::Metal.dispatch'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:67:in 'ActionDispatch::Routing::RouteSet::Dispatcher#dispatch'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:50:in 'ActionDispatch::Routing::RouteSet::Dispatcher#serve'
actionpack (*******) lib/action_dispatch/journey/router.rb:53:in 'block in ActionDispatch::Journey::Router#serve'
actionpack (*******) lib/action_dispatch/journey/router.rb:133:in 'block in ActionDispatch::Journey::Router#find_routes'
actionpack (*******) lib/action_dispatch/journey/router.rb:126:in 'Array#each'
actionpack (*******) lib/action_dispatch/journey/router.rb:126:in 'ActionDispatch::Journey::Router#find_routes'
actionpack (*******) lib/action_dispatch/journey/router.rb:34:in 'ActionDispatch::Journey::Router#serve'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:908:in 'ActionDispatch::Routing::RouteSet#call'
railties (*******) lib/rails/engine/lazy_route_set.rb:68:in 'Rails::Engine::LazyRouteSet#call'
rack (3.2.0) lib/rack/tempfile_reaper.rb:20:in 'Rack::TempfileReaper#call'
rack (3.2.0) lib/rack/etag.rb:29:in 'Rack::ETag#call'
rack (3.2.0) lib/rack/conditional_get.rb:31:in 'Rack::ConditionalGet#call'
rack (3.2.0) lib/rack/head.rb:15:in 'Rack::Head#call'
actionpack (*******) lib/action_dispatch/http/permissions_policy.rb:38:in 'ActionDispatch::PermissionsPolicy::Middleware#call'
actionpack (*******) lib/action_dispatch/http/content_security_policy.rb:38:in 'ActionDispatch::ContentSecurityPolicy::Middleware#call'
rack-session (2.1.1) lib/rack/session/abstract/id.rb:274:in 'Rack::Session::Abstract::Persisted#context'
rack-session (2.1.1) lib/rack/session/abstract/id.rb:268:in 'Rack::Session::Abstract::Persisted#call'
actionpack (*******) lib/action_dispatch/middleware/cookies.rb:706:in 'ActionDispatch::Cookies#call'
activerecord (*******) lib/active_record/migration.rb:671:in 'ActiveRecord::Migration::CheckPending#call'
actionpack (*******) lib/action_dispatch/middleware/callbacks.rb:31:in 'block in ActionDispatch::Callbacks#call'
activesupport (*******) lib/active_support/callbacks.rb:100:in 'ActiveSupport::Callbacks#run_callbacks'
actionpack (*******) lib/action_dispatch/middleware/callbacks.rb:30:in 'ActionDispatch::Callbacks#call'
actionpack (*******) lib/action_dispatch/middleware/executor.rb:16:in 'ActionDispatch::Executor#call'
actionpack (*******) lib/action_dispatch/middleware/actionable_exceptions.rb:18:in 'ActionDispatch::ActionableExceptions#call'
actionpack (*******) lib/action_dispatch/middleware/debug_exceptions.rb:31:in 'ActionDispatch::DebugExceptions#call'
web-console (4.2.1) lib/web_console/middleware.rb:132:in 'WebConsole::Middleware#call_app'
web-console (4.2.1) lib/web_console/middleware.rb:28:in 'block in WebConsole::Middleware#call'
web-console (4.2.1) lib/web_console/middleware.rb:17:in 'Kernel#catch'
web-console (4.2.1) lib/web_console/middleware.rb:17:in 'WebConsole::Middleware#call'
actionpack (*******) lib/action_dispatch/middleware/show_exceptions.rb:32:in 'ActionDispatch::ShowExceptions#call'
railties (*******) lib/rails/rack/logger.rb:41:in 'Rails::Rack::Logger#call_app'
railties (*******) lib/rails/rack/logger.rb:29:in 'Rails::Rack::Logger#call'
actionpack (*******) lib/action_dispatch/middleware/remote_ip.rb:96:in 'ActionDispatch::RemoteIp#call'
actionpack (*******) lib/action_dispatch/middleware/request_id.rb:34:in 'ActionDispatch::RequestId#call'
rack (3.2.0) lib/rack/method_override.rb:28:in 'Rack::MethodOverride#call'
rack (3.2.0) lib/rack/runtime.rb:24:in 'Rack::Runtime#call'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:61:in 'block in ActionDispatch::ServerTiming#call'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:26:in 'ActionDispatch::ServerTiming::Subscriber#collect_events'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:60:in 'ActionDispatch::ServerTiming#call'
actionpack (*******) lib/action_dispatch/middleware/executor.rb:16:in 'ActionDispatch::Executor#call'
actionpack (*******) lib/action_dispatch/middleware/static.rb:27:in 'ActionDispatch::Static#call'
rack (3.2.0) lib/rack/sendfile.rb:114:in 'Rack::Sendfile#call'
actionpack (*******) lib/action_dispatch/middleware/host_authorization.rb:143:in 'ActionDispatch::HostAuthorization#call'
railties (*******) lib/rails/engine.rb:535:in 'Rails::Engine#call'
puma (6.6.1) lib/puma/configuration.rb:279:in 'Puma::Configuration::ConfigMiddleware#call'
puma (6.6.1) lib/puma/request.rb:99:in 'block in Puma::Request#handle_request'
puma (6.6.1) lib/puma/thread_pool.rb:390:in 'Puma::ThreadPool#with_force_shutdown'
puma (6.6.1) lib/puma/request.rb:98:in 'Puma::Request#handle_request'
puma (6.6.1) lib/puma/server.rb:472:in 'Puma::Server#process_client'
puma (6.6.1) lib/puma/server.rb:254:in 'block in Puma::Server#run'
puma (6.6.1) lib/puma/thread_pool.rb:167:in 'block in Puma::ThreadPool#spawn_thread'
Started GET "/" for ::1 at 2025-08-22 03:07:16 +0600
Processing by HomeController#index as HTML
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.5ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.1ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.2ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.3ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (0.3ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (2, 3, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.4ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 25.9ms | GC: 3.4ms)
  [1m[36mUser Load (0.4ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = 4 LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/controllers/application_controller.rb:8:in 'ApplicationController#current_user'
  Rendered layout layouts/application.html.erb (Duration: 28.8ms | GC: 4.2ms)
Completed 200 OK in 63ms (Views: 22.6ms | ActiveRecord: 7.1ms (48 queries, 20 cached) | GC: 4.2ms)


Started GET "/categories/8" for ::1 at 2025-08-22 03:07:16 +0600
Processing by CategoriesController#show as HTML
  Parameters: {"id" => "8"}
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" = 8 LIMIT 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/controllers/categories_controller.rb:15:in 'CategoriesController#set_category'
  Rendering layout layouts/application.html.erb
  Rendering categories/show.html.erb within layouts/application
  [1m[36mEntity Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 AND "entities"."active" = TRUE /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:31
  [1m[36mEntity Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "entities" WHERE "entities"."category_id" = 8 AND "entities"."active" = TRUE LIMIT 1 OFFSET 0 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:37
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = 4 LIMIT 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/controllers/application_controller.rb:8:in 'ApplicationController#current_user'
  Rendered categories/show.html.erb within layouts/application (Duration: 16.9ms | GC: 1.0ms)
  Rendered layout layouts/application.html.erb (Duration: 17.7ms | GC: 1.0ms)
Completed 200 OK in 55ms (Views: 17.2ms | ActiveRecord: 1.3ms (4 queries, 0 cached) | GC: 1.0ms)


Started GET "/" for ::1 at 2025-08-22 03:07:18 +0600
Processing by HomeController#index as HTML
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (0.7ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.5ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.3ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.2ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (2, 3, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 44.9ms | GC: 21.9ms)
  [1m[36mUser Load (0.5ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = 4 LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/controllers/application_controller.rb:8:in 'ApplicationController#current_user'
  Rendered layout layouts/application.html.erb (Duration: 52.8ms | GC: 25.1ms)
Completed 200 OK in 91ms (Views: 48.2ms | ActiveRecord: 6.7ms (48 queries, 20 cached) | GC: 25.1ms)


Started GET "/" for ::1 at 2025-08-22 03:07:19 +0600
Processing by HomeController#index as HTML
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.5ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.4ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.2ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.2ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (0.3ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (2, 3, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 46.9ms | GC: 22.4ms)
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = 4 LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/controllers/application_controller.rb:8:in 'ApplicationController#current_user'
  Rendered layout layouts/application.html.erb (Duration: 50.0ms | GC: 23.1ms)
Completed 200 OK in 95ms (Views: 44.2ms | ActiveRecord: 6.5ms (48 queries, 20 cached) | GC: 23.8ms)


Started GET "/entities" for ::1 at 2025-08-22 03:07:20 +0600
Processing by EntitiesController#index as HTML
Completed 404 Not Found in 39ms (ActiveRecord: 0.0ms (0 queries, 0 cached) | GC: 0.0ms)


  
AbstractController::ActionNotFound (The reviews action could not be found for the :set_entity
callback on EntitiesController, but it is listed in the controller's
:only option.

Raising for missing callback actions is a new default in Rails 7.1, if you'd
like to turn this off you can delete the option from the environment configurations
or set `config.action_controller.raise_on_missing_callback_actions` to `false`.
):
  
actionpack (*******) lib/abstract_controller/callbacks.rb:62:in 'AbstractController::Callbacks::ActionFilter#match?'
activesupport (*******) lib/active_support/callbacks.rb:384:in 'block in ActiveSupport::Callbacks::CallTemplate::ObjectCall#make_lambda'
activesupport (*******) lib/active_support/callbacks.rb:177:in 'block in ActiveSupport::Callbacks::Filters::Before#call'
activesupport (*******) lib/active_support/callbacks.rb:177:in 'Array#all?'
activesupport (*******) lib/active_support/callbacks.rb:177:in 'ActiveSupport::Callbacks::Filters::Before#call'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'block in ActiveSupport::Callbacks::CallbackSequence#invoke_before'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'Array#each'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'ActiveSupport::Callbacks::CallbackSequence#invoke_before'
activesupport (*******) lib/active_support/callbacks.rb:118:in 'block in ActiveSupport::Callbacks#run_callbacks'
turbo-rails (2.0.16) lib/turbo-rails.rb:24:in 'Turbo.with_request_id'
turbo-rails (2.0.16) app/controllers/concerns/turbo/request_id_tracking.rb:10:in 'Turbo::RequestIdTracking#turbo_tracking_request_id'
activesupport (*******) lib/active_support/callbacks.rb:129:in 'block in ActiveSupport::Callbacks#run_callbacks'
actiontext (*******) lib/action_text/rendering.rb:25:in 'ActionText::Rendering::ClassMethods#with_renderer'
actiontext (*******) lib/action_text/engine.rb:71:in 'block (4 levels) in <class:Engine>'
activesupport (*******) lib/active_support/callbacks.rb:129:in 'BasicObject#instance_exec'
activesupport (*******) lib/active_support/callbacks.rb:129:in 'block in ActiveSupport::Callbacks#run_callbacks'
activesupport (*******) lib/active_support/callbacks.rb:140:in 'ActiveSupport::Callbacks#run_callbacks'
actionpack (*******) lib/abstract_controller/callbacks.rb:260:in 'AbstractController::Callbacks#process_action'
actionpack (*******) lib/action_controller/metal/rescue.rb:27:in 'ActionController::Rescue#process_action'
actionpack (*******) lib/action_controller/metal/instrumentation.rb:76:in 'block in ActionController::Instrumentation#process_action'
activesupport (*******) lib/active_support/notifications.rb:210:in 'block in ActiveSupport::Notifications.instrument'
activesupport (*******) lib/active_support/notifications/instrumenter.rb:58:in 'ActiveSupport::Notifications::Instrumenter#instrument'
activesupport (*******) lib/active_support/notifications.rb:210:in 'ActiveSupport::Notifications.instrument'
actionpack (*******) lib/action_controller/metal/instrumentation.rb:75:in 'ActionController::Instrumentation#process_action'
actionpack (*******) lib/action_controller/metal/params_wrapper.rb:259:in 'ActionController::ParamsWrapper#process_action'
activerecord (*******) lib/active_record/railties/controller_runtime.rb:39:in 'ActiveRecord::Railties::ControllerRuntime#process_action'
actionpack (*******) lib/abstract_controller/base.rb:163:in 'AbstractController::Base#process'
actionview (*******) lib/action_view/rendering.rb:40:in 'ActionView::Rendering#process'
actionpack (*******) lib/action_controller/metal.rb:252:in 'ActionController::Metal#dispatch'
actionpack (*******) lib/action_controller/metal.rb:335:in 'ActionController::Metal.dispatch'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:67:in 'ActionDispatch::Routing::RouteSet::Dispatcher#dispatch'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:50:in 'ActionDispatch::Routing::RouteSet::Dispatcher#serve'
actionpack (*******) lib/action_dispatch/journey/router.rb:53:in 'block in ActionDispatch::Journey::Router#serve'
actionpack (*******) lib/action_dispatch/journey/router.rb:133:in 'block in ActionDispatch::Journey::Router#find_routes'
actionpack (*******) lib/action_dispatch/journey/router.rb:126:in 'Array#each'
actionpack (*******) lib/action_dispatch/journey/router.rb:126:in 'ActionDispatch::Journey::Router#find_routes'
actionpack (*******) lib/action_dispatch/journey/router.rb:34:in 'ActionDispatch::Journey::Router#serve'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:908:in 'ActionDispatch::Routing::RouteSet#call'
railties (*******) lib/rails/engine/lazy_route_set.rb:68:in 'Rails::Engine::LazyRouteSet#call'
rack (3.2.0) lib/rack/tempfile_reaper.rb:20:in 'Rack::TempfileReaper#call'
rack (3.2.0) lib/rack/etag.rb:29:in 'Rack::ETag#call'
rack (3.2.0) lib/rack/conditional_get.rb:31:in 'Rack::ConditionalGet#call'
rack (3.2.0) lib/rack/head.rb:15:in 'Rack::Head#call'
actionpack (*******) lib/action_dispatch/http/permissions_policy.rb:38:in 'ActionDispatch::PermissionsPolicy::Middleware#call'
actionpack (*******) lib/action_dispatch/http/content_security_policy.rb:38:in 'ActionDispatch::ContentSecurityPolicy::Middleware#call'
rack-session (2.1.1) lib/rack/session/abstract/id.rb:274:in 'Rack::Session::Abstract::Persisted#context'
rack-session (2.1.1) lib/rack/session/abstract/id.rb:268:in 'Rack::Session::Abstract::Persisted#call'
actionpack (*******) lib/action_dispatch/middleware/cookies.rb:706:in 'ActionDispatch::Cookies#call'
activerecord (*******) lib/active_record/migration.rb:671:in 'ActiveRecord::Migration::CheckPending#call'
actionpack (*******) lib/action_dispatch/middleware/callbacks.rb:31:in 'block in ActionDispatch::Callbacks#call'
activesupport (*******) lib/active_support/callbacks.rb:100:in 'ActiveSupport::Callbacks#run_callbacks'
actionpack (*******) lib/action_dispatch/middleware/callbacks.rb:30:in 'ActionDispatch::Callbacks#call'
actionpack (*******) lib/action_dispatch/middleware/executor.rb:16:in 'ActionDispatch::Executor#call'
actionpack (*******) lib/action_dispatch/middleware/actionable_exceptions.rb:18:in 'ActionDispatch::ActionableExceptions#call'
actionpack (*******) lib/action_dispatch/middleware/debug_exceptions.rb:31:in 'ActionDispatch::DebugExceptions#call'
web-console (4.2.1) lib/web_console/middleware.rb:132:in 'WebConsole::Middleware#call_app'
web-console (4.2.1) lib/web_console/middleware.rb:28:in 'block in WebConsole::Middleware#call'
web-console (4.2.1) lib/web_console/middleware.rb:17:in 'Kernel#catch'
web-console (4.2.1) lib/web_console/middleware.rb:17:in 'WebConsole::Middleware#call'
actionpack (*******) lib/action_dispatch/middleware/show_exceptions.rb:32:in 'ActionDispatch::ShowExceptions#call'
railties (*******) lib/rails/rack/logger.rb:41:in 'Rails::Rack::Logger#call_app'
railties (*******) lib/rails/rack/logger.rb:29:in 'Rails::Rack::Logger#call'
actionpack (*******) lib/action_dispatch/middleware/remote_ip.rb:96:in 'ActionDispatch::RemoteIp#call'
actionpack (*******) lib/action_dispatch/middleware/request_id.rb:34:in 'ActionDispatch::RequestId#call'
rack (3.2.0) lib/rack/method_override.rb:28:in 'Rack::MethodOverride#call'
rack (3.2.0) lib/rack/runtime.rb:24:in 'Rack::Runtime#call'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:61:in 'block in ActionDispatch::ServerTiming#call'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:26:in 'ActionDispatch::ServerTiming::Subscriber#collect_events'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:60:in 'ActionDispatch::ServerTiming#call'
actionpack (*******) lib/action_dispatch/middleware/executor.rb:16:in 'ActionDispatch::Executor#call'
actionpack (*******) lib/action_dispatch/middleware/static.rb:27:in 'ActionDispatch::Static#call'
rack (3.2.0) lib/rack/sendfile.rb:114:in 'Rack::Sendfile#call'
actionpack (*******) lib/action_dispatch/middleware/host_authorization.rb:143:in 'ActionDispatch::HostAuthorization#call'
railties (*******) lib/rails/engine.rb:535:in 'Rails::Engine#call'
puma (6.6.1) lib/puma/configuration.rb:279:in 'Puma::Configuration::ConfigMiddleware#call'
puma (6.6.1) lib/puma/request.rb:99:in 'block in Puma::Request#handle_request'
puma (6.6.1) lib/puma/thread_pool.rb:390:in 'Puma::ThreadPool#with_force_shutdown'
puma (6.6.1) lib/puma/request.rb:98:in 'Puma::Request#handle_request'
puma (6.6.1) lib/puma/server.rb:472:in 'Puma::Server#process_client'
puma (6.6.1) lib/puma/server.rb:254:in 'block in Puma::Server#run'
puma (6.6.1) lib/puma/thread_pool.rb:167:in 'block in Puma::ThreadPool#spawn_thread'
Started GET "/" for ::1 at 2025-08-22 03:07:21 +0600
Processing by HomeController#index as HTML
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.4ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.3ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.1ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.3ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (0.3ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (2, 3, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 26.6ms | GC: 5.4ms)
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = 4 LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/controllers/application_controller.rb:8:in 'ApplicationController#current_user'
  Rendered layout layouts/application.html.erb (Duration: 29.4ms | GC: 5.4ms)
Completed 200 OK in 69ms (Views: 24.5ms | ActiveRecord: 5.1ms (48 queries, 20 cached) | GC: 5.4ms)


Started GET "/categories/1" for ::1 at 2025-08-22 03:07:23 +0600
Processing by CategoriesController#show as HTML
  Parameters: {"id" => "1"}
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" = 1 LIMIT 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/controllers/categories_controller.rb:15:in 'CategoriesController#set_category'
  Rendering layout layouts/application.html.erb
  Rendering categories/show.html.erb within layouts/application
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 AND "entities"."active" = TRUE /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:31
  [1m[36mEntity Exists? (0.1ms)[0m  [1m[34mSELECT 1 AS one FROM "entities" WHERE "entities"."category_id" = 1 AND "entities"."active" = TRUE LIMIT 1 OFFSET 0 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:37
  [1m[36mEntity Load (0.1ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" = 1 AND "entities"."active" = TRUE LIMIT 12 OFFSET 0 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:39
  [1m[36mReview Load (0.1ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/views/categories/show.html.erb:39
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  Rendered categories/show.html.erb within layouts/application (Duration: 9.0ms | GC: 1.5ms)
  Rendered layout layouts/application.html.erb (Duration: 9.1ms | GC: 1.5ms)
Completed 500 Internal Server Error in 56ms (ActiveRecord: 1.2ms (11 queries, 4 cached) | GC: 1.5ms)


  
ActionView::Template::Error (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
)
Caused by: ActionView::MissingTemplate (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
)

Information for: ActionView::Template::Error (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
):
    89:   </div>
    90: 
    91:   <!-- Pagination -->
    92:   <%= paginate @entities, theme: 'twitter-bootstrap-4' if respond_to?(:paginate) %>
    93: <% else %>
    94:   <div class="text-center py-12">
    95:     <div class="text-6xl mb-4">
  
app/views/categories/show.html.erb:92

Information for cause: ActionView::MissingTemplate (Missing partial kaminari/twitter-bootstrap-4/_paginator with {locale: [:en], formats: [:html, :html], variants: [], handlers: [:raw, :erb, :html, :builder, :ruby, :jbuilder]}.

Searched in:
  * "/Users/<USER>/CLIENTS/cloudvision-app/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/kaminari-core-1.2.2/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/turbo-rails-2.0.16/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actiontext-*******/app/views"
  * "/Users/<USER>/.local/share/mise/installs/ruby/3.4.5/lib/ruby/gems/3.4.0/gems/actionmailbox-*******/app/views"
):
  
app/views/categories/show.html.erb:92
Started GET "/google_places/search" for ::1 at 2025-08-22 03:07:31 +0600
Processing by GooglePlacesController#search as HTML
  [1m[36mUser Load (0.7ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = 4 LIMIT 1 /*action='search',application='CloudvisionApp',controller='google_places'*/[0m
  ↳ app/controllers/application_controller.rb:8:in 'ApplicationController#current_user'
  Rendering layout layouts/application.html.erb
  Rendering google_places/search.html.erb within layouts/application
  Rendered google_places/search.html.erb within layouts/application (Duration: 6.4ms | GC: 0.0ms)
  Rendered layout layouts/application.html.erb (Duration: 8.3ms | GC: 0.0ms)
Completed 200 OK in 48ms (Views: 9.5ms | ActiveRecord: 0.7ms (1 query, 0 cached) | GC: 0.6ms)


Started GET "/google_places/search?q=Thimphu&location=&commit=Search+Google+Places" for ::1 at 2025-08-22 03:07:37 +0600
Processing by GooglePlacesController#search as HTML
  Parameters: {"q" => "Thimphu", "location" => "", "commit" => "Search Google Places"}
  [1m[36mUser Load (0.4ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = 4 LIMIT 1 /*action='search',application='CloudvisionApp',controller='google_places'*/[0m
  ↳ app/controllers/application_controller.rb:8:in 'ApplicationController#current_user'
  Rendering layout layouts/application.html.erb
  Rendering google_places/search.html.erb within layouts/application
  Rendered google_places/search.html.erb within layouts/application (Duration: 6.4ms | GC: 0.0ms)
  Rendered layout layouts/application.html.erb (Duration: 7.6ms | GC: 0.8ms)
Completed 200 OK in 50ms (Views: 8.3ms | ActiveRecord: 0.4ms (1 query, 0 cached) | GC: 1.3ms)


Started GET "/google_places/search?q=Thimphu&location=&commit=Search+Google+Places" for ::1 at 2025-08-22 03:07:47 +0600
Processing by GooglePlacesController#search as HTML
  Parameters: {"q" => "Thimphu", "location" => "", "commit" => "Search Google Places"}
  [1m[36mUser Load (0.5ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = 4 LIMIT 1 /*action='search',application='CloudvisionApp',controller='google_places'*/[0m
  ↳ app/controllers/application_controller.rb:8:in 'ApplicationController#current_user'
  Rendering layout layouts/application.html.erb
  Rendering google_places/search.html.erb within layouts/application
  Rendered google_places/search.html.erb within layouts/application (Duration: 13.0ms | GC: 0.4ms)
  Rendered layout layouts/application.html.erb (Duration: 13.7ms | GC: 0.4ms)
Completed 200 OK in 48ms (Views: 13.9ms | ActiveRecord: 0.5ms (1 query, 0 cached) | GC: 0.4ms)


Started POST "/google_places/import" for ::1 at 2025-08-22 03:07:50 +0600
Processing by GooglePlacesController#import as TURBO_STREAM
  Parameters: {"authenticity_token" => "[FILTERED]", "place_data" => "{\"google_place_id\":\"mock_place_0_1755810467\",\"name\":\"Artisan Coffee Roasters\",\"address\":\"100 Main Street, \",\"latitude\":37.78480113917145,\"longitude\":-122.41105141035297,\"rating\":4.6,\"types\":[\"cafe\",\"store\"],\"price_level\":3,\"photo_reference\":null}", "commit" => "Import This Place"}
  [1m[36mUser Load (0.6ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = 4 LIMIT 1 /*action='import',application='CloudvisionApp',controller='google_places'*/[0m
  ↳ app/controllers/application_controller.rb:8:in 'ApplicationController#current_user'
Completed 500 Internal Server Error in 37ms (ActiveRecord: 0.6ms (1 query, 0 cached) | GC: 1.2ms)


  
TypeError (no implicit conversion of Symbol into Integer):
  
app/controllers/google_places_controller.rb:25:in 'String#[]'
app/controllers/google_places_controller.rb:25:in 'GooglePlacesController#import'
Started GET "/entities/new" for ::1 at 2025-08-22 03:08:31 +0600
Processing by EntitiesController#new as */*
Redirected to http://localhost:3000/sign_in
Filter chain halted as :authenticate_user! rendered or redirected
Completed 302 Found in 36ms (ActiveRecord: 0.0ms (0 queries, 0 cached) | GC: 0.0ms)


  [1m[36mUser Load (3.3ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
Started GET "/google_places/search?q=Thimphu&location=&commit=Search+Google+Places" for ::1 at 2025-08-22 03:10:43 +0600
Processing by GooglePlacesController#search as HTML
  Parameters: {"q" => "Thimphu", "location" => "", "commit" => "Search Google Places"}
  [1m[36mUser Load (0.3ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = 4 LIMIT 1 /*action='search',application='CloudvisionApp',controller='google_places'*/[0m
  ↳ app/controllers/application_controller.rb:9:in 'ApplicationController#current_user'
  Rendering layout layouts/application.html.erb
  Rendering google_places/search.html.erb within layouts/application
  Rendered google_places/search.html.erb within layouts/application (Duration: 20.4ms | GC: 1.0ms)
  Rendered layout layouts/application.html.erb (Duration: 28.2ms | GC: 1.4ms)
Completed 200 OK in 161ms (Views: 31.6ms | ActiveRecord: 6.7ms (1 query, 0 cached) | GC: 4.2ms)


Started POST "/google_places/import" for ::1 at 2025-08-22 03:10:48 +0600
Processing by GooglePlacesController#import as TURBO_STREAM
  Parameters: {"authenticity_token" => "[FILTERED]", "place_data" => "{\"google_place_id\":\"mock_place_0_1755810643\",\"name\":\"Mama's Italian Kitchen\",\"address\":\"100 Main Street, \",\"latitude\":37.78178589201352,\"longitude\":-122.42640999264466,\"rating\":4.2,\"types\":[\"restaurant\",\"meal_takeaway\"],\"price_level\":1,\"photo_reference\":null}", "commit" => "Import This Place"}
  [1m[36mUser Load (0.6ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = 4 LIMIT 1 /*action='import',application='CloudvisionApp',controller='google_places'*/[0m
  ↳ app/controllers/application_controller.rb:9:in 'ApplicationController#current_user'
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Restaurants' LIMIT 1 /*action='import',application='CloudvisionApp',controller='google_places'*/[0m
  ↳ app/controllers/google_places_controller.rb:87:in 'GooglePlacesController#map_google_type_to_category'
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*action='import',application='CloudvisionApp',controller='google_places'*/[0m
  ↳ app/controllers/google_places_controller.rb:41:in 'GooglePlacesController#import'
  [1m[36mEntity Create (2.0ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('Mama''s Italian Kitchen', 'Imported from Google Places', '100 Main Street, ', NULL, NULL, NULL, 37.781786, -122.42641, TRUE, 1, 4, '2025-08-21 21:10:48.348098', '2025-08-21 21:10:48.348098') RETURNING "id" /*action='import',application='CloudvisionApp',controller='google_places'*/[0m
  ↳ app/controllers/google_places_controller.rb:41:in 'GooglePlacesController#import'
  [1m[36mTRANSACTION (1.3ms)[0m  [1m[35mCOMMIT /*action='import',application='CloudvisionApp',controller='google_places'*/[0m
  ↳ app/controllers/google_places_controller.rb:41:in 'GooglePlacesController#import'
Redirected to http://localhost:3000/entities/6
Completed 302 Found in 137ms (ActiveRecord: 14.0ms (3 queries, 0 cached) | GC: 1.8ms)


Started GET "/entities/6" for ::1 at 2025-08-22 03:10:48 +0600
Processing by EntitiesController#show as TURBO_STREAM
  Parameters: {"id" => "6"}
Completed 404 Not Found in 31ms (ActiveRecord: 0.0ms (0 queries, 0 cached) | GC: 0.0ms)


  
AbstractController::ActionNotFound (The reviews action could not be found for the :set_entity
callback on EntitiesController, but it is listed in the controller's
:only option.

Raising for missing callback actions is a new default in Rails 7.1, if you'd
like to turn this off you can delete the option from the environment configurations
or set `config.action_controller.raise_on_missing_callback_actions` to `false`.
):
  
actionpack (*******) lib/abstract_controller/callbacks.rb:62:in 'AbstractController::Callbacks::ActionFilter#match?'
activesupport (*******) lib/active_support/callbacks.rb:384:in 'block in ActiveSupport::Callbacks::CallTemplate::ObjectCall#make_lambda'
activesupport (*******) lib/active_support/callbacks.rb:177:in 'block in ActiveSupport::Callbacks::Filters::Before#call'
activesupport (*******) lib/active_support/callbacks.rb:177:in 'Array#all?'
activesupport (*******) lib/active_support/callbacks.rb:177:in 'ActiveSupport::Callbacks::Filters::Before#call'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'block in ActiveSupport::Callbacks::CallbackSequence#invoke_before'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'Array#each'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'ActiveSupport::Callbacks::CallbackSequence#invoke_before'
activesupport (*******) lib/active_support/callbacks.rb:118:in 'block in ActiveSupport::Callbacks#run_callbacks'
turbo-rails (2.0.16) lib/turbo-rails.rb:24:in 'Turbo.with_request_id'
turbo-rails (2.0.16) app/controllers/concerns/turbo/request_id_tracking.rb:10:in 'Turbo::RequestIdTracking#turbo_tracking_request_id'
activesupport (*******) lib/active_support/callbacks.rb:129:in 'block in ActiveSupport::Callbacks#run_callbacks'
actiontext (*******) lib/action_text/rendering.rb:25:in 'ActionText::Rendering::ClassMethods#with_renderer'
actiontext (*******) lib/action_text/engine.rb:71:in 'block (4 levels) in <class:Engine>'
activesupport (*******) lib/active_support/callbacks.rb:129:in 'BasicObject#instance_exec'
activesupport (*******) lib/active_support/callbacks.rb:129:in 'block in ActiveSupport::Callbacks#run_callbacks'
activesupport (*******) lib/active_support/callbacks.rb:140:in 'ActiveSupport::Callbacks#run_callbacks'
actionpack (*******) lib/abstract_controller/callbacks.rb:260:in 'AbstractController::Callbacks#process_action'
actionpack (*******) lib/action_controller/metal/rescue.rb:27:in 'ActionController::Rescue#process_action'
actionpack (*******) lib/action_controller/metal/instrumentation.rb:76:in 'block in ActionController::Instrumentation#process_action'
activesupport (*******) lib/active_support/notifications.rb:210:in 'block in ActiveSupport::Notifications.instrument'
activesupport (*******) lib/active_support/notifications/instrumenter.rb:58:in 'ActiveSupport::Notifications::Instrumenter#instrument'
activesupport (*******) lib/active_support/notifications.rb:210:in 'ActiveSupport::Notifications.instrument'
actionpack (*******) lib/action_controller/metal/instrumentation.rb:75:in 'ActionController::Instrumentation#process_action'
actionpack (*******) lib/action_controller/metal/params_wrapper.rb:259:in 'ActionController::ParamsWrapper#process_action'
activerecord (*******) lib/active_record/railties/controller_runtime.rb:39:in 'ActiveRecord::Railties::ControllerRuntime#process_action'
actionpack (*******) lib/abstract_controller/base.rb:163:in 'AbstractController::Base#process'
actionview (*******) lib/action_view/rendering.rb:40:in 'ActionView::Rendering#process'
actionpack (*******) lib/action_controller/metal.rb:252:in 'ActionController::Metal#dispatch'
actionpack (*******) lib/action_controller/metal.rb:335:in 'ActionController::Metal.dispatch'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:67:in 'ActionDispatch::Routing::RouteSet::Dispatcher#dispatch'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:50:in 'ActionDispatch::Routing::RouteSet::Dispatcher#serve'
actionpack (*******) lib/action_dispatch/journey/router.rb:53:in 'block in ActionDispatch::Journey::Router#serve'
actionpack (*******) lib/action_dispatch/journey/router.rb:133:in 'block in ActionDispatch::Journey::Router#find_routes'
actionpack (*******) lib/action_dispatch/journey/router.rb:126:in 'Array#each'
actionpack (*******) lib/action_dispatch/journey/router.rb:126:in 'ActionDispatch::Journey::Router#find_routes'
actionpack (*******) lib/action_dispatch/journey/router.rb:34:in 'ActionDispatch::Journey::Router#serve'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:908:in 'ActionDispatch::Routing::RouteSet#call'
railties (*******) lib/rails/engine/lazy_route_set.rb:68:in 'Rails::Engine::LazyRouteSet#call'
rack (3.2.0) lib/rack/tempfile_reaper.rb:20:in 'Rack::TempfileReaper#call'
rack (3.2.0) lib/rack/etag.rb:29:in 'Rack::ETag#call'
rack (3.2.0) lib/rack/conditional_get.rb:31:in 'Rack::ConditionalGet#call'
rack (3.2.0) lib/rack/head.rb:15:in 'Rack::Head#call'
actionpack (*******) lib/action_dispatch/http/permissions_policy.rb:38:in 'ActionDispatch::PermissionsPolicy::Middleware#call'
actionpack (*******) lib/action_dispatch/http/content_security_policy.rb:38:in 'ActionDispatch::ContentSecurityPolicy::Middleware#call'
rack-session (2.1.1) lib/rack/session/abstract/id.rb:274:in 'Rack::Session::Abstract::Persisted#context'
rack-session (2.1.1) lib/rack/session/abstract/id.rb:268:in 'Rack::Session::Abstract::Persisted#call'
actionpack (*******) lib/action_dispatch/middleware/cookies.rb:706:in 'ActionDispatch::Cookies#call'
activerecord (*******) lib/active_record/migration.rb:671:in 'ActiveRecord::Migration::CheckPending#call'
actionpack (*******) lib/action_dispatch/middleware/callbacks.rb:31:in 'block in ActionDispatch::Callbacks#call'
activesupport (*******) lib/active_support/callbacks.rb:100:in 'ActiveSupport::Callbacks#run_callbacks'
actionpack (*******) lib/action_dispatch/middleware/callbacks.rb:30:in 'ActionDispatch::Callbacks#call'
actionpack (*******) lib/action_dispatch/middleware/executor.rb:16:in 'ActionDispatch::Executor#call'
actionpack (*******) lib/action_dispatch/middleware/actionable_exceptions.rb:18:in 'ActionDispatch::ActionableExceptions#call'
actionpack (*******) lib/action_dispatch/middleware/debug_exceptions.rb:31:in 'ActionDispatch::DebugExceptions#call'
web-console (4.2.1) lib/web_console/middleware.rb:132:in 'WebConsole::Middleware#call_app'
web-console (4.2.1) lib/web_console/middleware.rb:28:in 'block in WebConsole::Middleware#call'
web-console (4.2.1) lib/web_console/middleware.rb:17:in 'Kernel#catch'
web-console (4.2.1) lib/web_console/middleware.rb:17:in 'WebConsole::Middleware#call'
actionpack (*******) lib/action_dispatch/middleware/show_exceptions.rb:32:in 'ActionDispatch::ShowExceptions#call'
railties (*******) lib/rails/rack/logger.rb:41:in 'Rails::Rack::Logger#call_app'
railties (*******) lib/rails/rack/logger.rb:29:in 'Rails::Rack::Logger#call'
actionpack (*******) lib/action_dispatch/middleware/remote_ip.rb:96:in 'ActionDispatch::RemoteIp#call'
actionpack (*******) lib/action_dispatch/middleware/request_id.rb:34:in 'ActionDispatch::RequestId#call'
rack (3.2.0) lib/rack/method_override.rb:28:in 'Rack::MethodOverride#call'
rack (3.2.0) lib/rack/runtime.rb:24:in 'Rack::Runtime#call'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:61:in 'block in ActionDispatch::ServerTiming#call'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:26:in 'ActionDispatch::ServerTiming::Subscriber#collect_events'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:60:in 'ActionDispatch::ServerTiming#call'
actionpack (*******) lib/action_dispatch/middleware/executor.rb:16:in 'ActionDispatch::Executor#call'
actionpack (*******) lib/action_dispatch/middleware/static.rb:27:in 'ActionDispatch::Static#call'
rack (3.2.0) lib/rack/sendfile.rb:114:in 'Rack::Sendfile#call'
actionpack (*******) lib/action_dispatch/middleware/host_authorization.rb:143:in 'ActionDispatch::HostAuthorization#call'
railties (*******) lib/rails/engine.rb:535:in 'Rails::Engine#call'
puma (6.6.1) lib/puma/configuration.rb:279:in 'Puma::Configuration::ConfigMiddleware#call'
puma (6.6.1) lib/puma/request.rb:99:in 'block in Puma::Request#handle_request'
puma (6.6.1) lib/puma/thread_pool.rb:390:in 'Puma::ThreadPool#with_force_shutdown'
puma (6.6.1) lib/puma/request.rb:98:in 'Puma::Request#handle_request'
puma (6.6.1) lib/puma/server.rb:472:in 'Puma::Server#process_client'
puma (6.6.1) lib/puma/server.rb:254:in 'block in Puma::Server#run'
puma (6.6.1) lib/puma/thread_pool.rb:167:in 'block in Puma::ThreadPool#spawn_thread'
Started GET "/google_places/search?q=Thimphu&location=&commit=Search+Google+Places" for ::1 at 2025-08-22 03:10:48 +0600
Processing by GooglePlacesController#search as HTML
  Parameters: {"q" => "Thimphu", "location" => "", "commit" => "Search Google Places"}
  [1m[36mUser Load (0.4ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = 4 LIMIT 1 /*action='search',application='CloudvisionApp',controller='google_places'*/[0m
  ↳ app/controllers/application_controller.rb:9:in 'ApplicationController#current_user'
  Rendering layout layouts/application.html.erb
  Rendering google_places/search.html.erb within layouts/application
  Rendered google_places/search.html.erb within layouts/application (Duration: 5.9ms | GC: 0.9ms)
  Rendered layout layouts/application.html.erb (Duration: 10.1ms | GC: 2.0ms)
Completed 200 OK in 45ms (Views: 10.5ms | ActiveRecord: 0.4ms (1 query, 0 cached) | GC: 2.0ms)


Started GET "/entities" for ::1 at 2025-08-22 03:10:55 +0600
Processing by EntitiesController#index as HTML
Completed 404 Not Found in 39ms (ActiveRecord: 0.0ms (0 queries, 0 cached) | GC: 0.0ms)


  
AbstractController::ActionNotFound (The reviews action could not be found for the :set_entity
callback on EntitiesController, but it is listed in the controller's
:only option.

Raising for missing callback actions is a new default in Rails 7.1, if you'd
like to turn this off you can delete the option from the environment configurations
or set `config.action_controller.raise_on_missing_callback_actions` to `false`.
):
  
actionpack (*******) lib/abstract_controller/callbacks.rb:62:in 'AbstractController::Callbacks::ActionFilter#match?'
activesupport (*******) lib/active_support/callbacks.rb:384:in 'block in ActiveSupport::Callbacks::CallTemplate::ObjectCall#make_lambda'
activesupport (*******) lib/active_support/callbacks.rb:177:in 'block in ActiveSupport::Callbacks::Filters::Before#call'
activesupport (*******) lib/active_support/callbacks.rb:177:in 'Array#all?'
activesupport (*******) lib/active_support/callbacks.rb:177:in 'ActiveSupport::Callbacks::Filters::Before#call'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'block in ActiveSupport::Callbacks::CallbackSequence#invoke_before'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'Array#each'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'ActiveSupport::Callbacks::CallbackSequence#invoke_before'
activesupport (*******) lib/active_support/callbacks.rb:118:in 'block in ActiveSupport::Callbacks#run_callbacks'
turbo-rails (2.0.16) lib/turbo-rails.rb:24:in 'Turbo.with_request_id'
turbo-rails (2.0.16) app/controllers/concerns/turbo/request_id_tracking.rb:10:in 'Turbo::RequestIdTracking#turbo_tracking_request_id'
activesupport (*******) lib/active_support/callbacks.rb:129:in 'block in ActiveSupport::Callbacks#run_callbacks'
actiontext (*******) lib/action_text/rendering.rb:25:in 'ActionText::Rendering::ClassMethods#with_renderer'
actiontext (*******) lib/action_text/engine.rb:71:in 'block (4 levels) in <class:Engine>'
activesupport (*******) lib/active_support/callbacks.rb:129:in 'BasicObject#instance_exec'
activesupport (*******) lib/active_support/callbacks.rb:129:in 'block in ActiveSupport::Callbacks#run_callbacks'
activesupport (*******) lib/active_support/callbacks.rb:140:in 'ActiveSupport::Callbacks#run_callbacks'
actionpack (*******) lib/abstract_controller/callbacks.rb:260:in 'AbstractController::Callbacks#process_action'
actionpack (*******) lib/action_controller/metal/rescue.rb:27:in 'ActionController::Rescue#process_action'
actionpack (*******) lib/action_controller/metal/instrumentation.rb:76:in 'block in ActionController::Instrumentation#process_action'
activesupport (*******) lib/active_support/notifications.rb:210:in 'block in ActiveSupport::Notifications.instrument'
activesupport (*******) lib/active_support/notifications/instrumenter.rb:58:in 'ActiveSupport::Notifications::Instrumenter#instrument'
activesupport (*******) lib/active_support/notifications.rb:210:in 'ActiveSupport::Notifications.instrument'
actionpack (*******) lib/action_controller/metal/instrumentation.rb:75:in 'ActionController::Instrumentation#process_action'
actionpack (*******) lib/action_controller/metal/params_wrapper.rb:259:in 'ActionController::ParamsWrapper#process_action'
activerecord (*******) lib/active_record/railties/controller_runtime.rb:39:in 'ActiveRecord::Railties::ControllerRuntime#process_action'
actionpack (*******) lib/abstract_controller/base.rb:163:in 'AbstractController::Base#process'
actionview (*******) lib/action_view/rendering.rb:40:in 'ActionView::Rendering#process'
actionpack (*******) lib/action_controller/metal.rb:252:in 'ActionController::Metal#dispatch'
actionpack (*******) lib/action_controller/metal.rb:335:in 'ActionController::Metal.dispatch'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:67:in 'ActionDispatch::Routing::RouteSet::Dispatcher#dispatch'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:50:in 'ActionDispatch::Routing::RouteSet::Dispatcher#serve'
actionpack (*******) lib/action_dispatch/journey/router.rb:53:in 'block in ActionDispatch::Journey::Router#serve'
actionpack (*******) lib/action_dispatch/journey/router.rb:133:in 'block in ActionDispatch::Journey::Router#find_routes'
actionpack (*******) lib/action_dispatch/journey/router.rb:126:in 'Array#each'
actionpack (*******) lib/action_dispatch/journey/router.rb:126:in 'ActionDispatch::Journey::Router#find_routes'
actionpack (*******) lib/action_dispatch/journey/router.rb:34:in 'ActionDispatch::Journey::Router#serve'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:908:in 'ActionDispatch::Routing::RouteSet#call'
railties (*******) lib/rails/engine/lazy_route_set.rb:68:in 'Rails::Engine::LazyRouteSet#call'
rack (3.2.0) lib/rack/tempfile_reaper.rb:20:in 'Rack::TempfileReaper#call'
rack (3.2.0) lib/rack/etag.rb:29:in 'Rack::ETag#call'
rack (3.2.0) lib/rack/conditional_get.rb:31:in 'Rack::ConditionalGet#call'
rack (3.2.0) lib/rack/head.rb:15:in 'Rack::Head#call'
actionpack (*******) lib/action_dispatch/http/permissions_policy.rb:38:in 'ActionDispatch::PermissionsPolicy::Middleware#call'
actionpack (*******) lib/action_dispatch/http/content_security_policy.rb:38:in 'ActionDispatch::ContentSecurityPolicy::Middleware#call'
rack-session (2.1.1) lib/rack/session/abstract/id.rb:274:in 'Rack::Session::Abstract::Persisted#context'
rack-session (2.1.1) lib/rack/session/abstract/id.rb:268:in 'Rack::Session::Abstract::Persisted#call'
actionpack (*******) lib/action_dispatch/middleware/cookies.rb:706:in 'ActionDispatch::Cookies#call'
activerecord (*******) lib/active_record/migration.rb:671:in 'ActiveRecord::Migration::CheckPending#call'
actionpack (*******) lib/action_dispatch/middleware/callbacks.rb:31:in 'block in ActionDispatch::Callbacks#call'
activesupport (*******) lib/active_support/callbacks.rb:100:in 'ActiveSupport::Callbacks#run_callbacks'
actionpack (*******) lib/action_dispatch/middleware/callbacks.rb:30:in 'ActionDispatch::Callbacks#call'
actionpack (*******) lib/action_dispatch/middleware/executor.rb:16:in 'ActionDispatch::Executor#call'
actionpack (*******) lib/action_dispatch/middleware/actionable_exceptions.rb:18:in 'ActionDispatch::ActionableExceptions#call'
actionpack (*******) lib/action_dispatch/middleware/debug_exceptions.rb:31:in 'ActionDispatch::DebugExceptions#call'
web-console (4.2.1) lib/web_console/middleware.rb:132:in 'WebConsole::Middleware#call_app'
web-console (4.2.1) lib/web_console/middleware.rb:28:in 'block in WebConsole::Middleware#call'
web-console (4.2.1) lib/web_console/middleware.rb:17:in 'Kernel#catch'
web-console (4.2.1) lib/web_console/middleware.rb:17:in 'WebConsole::Middleware#call'
actionpack (*******) lib/action_dispatch/middleware/show_exceptions.rb:32:in 'ActionDispatch::ShowExceptions#call'
railties (*******) lib/rails/rack/logger.rb:41:in 'Rails::Rack::Logger#call_app'
railties (*******) lib/rails/rack/logger.rb:29:in 'Rails::Rack::Logger#call'
actionpack (*******) lib/action_dispatch/middleware/remote_ip.rb:96:in 'ActionDispatch::RemoteIp#call'
actionpack (*******) lib/action_dispatch/middleware/request_id.rb:34:in 'ActionDispatch::RequestId#call'
rack (3.2.0) lib/rack/method_override.rb:28:in 'Rack::MethodOverride#call'
rack (3.2.0) lib/rack/runtime.rb:24:in 'Rack::Runtime#call'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:61:in 'block in ActionDispatch::ServerTiming#call'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:26:in 'ActionDispatch::ServerTiming::Subscriber#collect_events'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:60:in 'ActionDispatch::ServerTiming#call'
actionpack (*******) lib/action_dispatch/middleware/executor.rb:16:in 'ActionDispatch::Executor#call'
actionpack (*******) lib/action_dispatch/middleware/static.rb:27:in 'ActionDispatch::Static#call'
rack (3.2.0) lib/rack/sendfile.rb:114:in 'Rack::Sendfile#call'
actionpack (*******) lib/action_dispatch/middleware/host_authorization.rb:143:in 'ActionDispatch::HostAuthorization#call'
railties (*******) lib/rails/engine.rb:535:in 'Rails::Engine#call'
puma (6.6.1) lib/puma/configuration.rb:279:in 'Puma::Configuration::ConfigMiddleware#call'
puma (6.6.1) lib/puma/request.rb:99:in 'block in Puma::Request#handle_request'
puma (6.6.1) lib/puma/thread_pool.rb:390:in 'Puma::ThreadPool#with_force_shutdown'
puma (6.6.1) lib/puma/request.rb:98:in 'Puma::Request#handle_request'
puma (6.6.1) lib/puma/server.rb:472:in 'Puma::Server#process_client'
puma (6.6.1) lib/puma/server.rb:254:in 'block in Puma::Server#run'
puma (6.6.1) lib/puma/thread_pool.rb:167:in 'block in Puma::ThreadPool#spawn_thread'
Started GET "/google_places/search?q=Thimphu&location=&commit=Search+Google+Places" for ::1 at 2025-08-22 03:10:57 +0600
Processing by GooglePlacesController#search as HTML
  Parameters: {"q" => "Thimphu", "location" => "", "commit" => "Search Google Places"}
  [1m[36mUser Load (0.4ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" = 4 LIMIT 1 /*action='search',application='CloudvisionApp',controller='google_places'*/[0m
  ↳ app/controllers/application_controller.rb:9:in 'ApplicationController#current_user'
  Rendering layout layouts/application.html.erb
  Rendering google_places/search.html.erb within layouts/application
  Rendered google_places/search.html.erb within layouts/application (Duration: 3.7ms | GC: 1.0ms)
  Rendered layout layouts/application.html.erb (Duration: 4.0ms | GC: 1.0ms)
Completed 200 OK in 52ms (Views: 4.9ms | ActiveRecord: 0.4ms (1 query, 0 cached) | GC: 1.0ms)


Started POST "/sign_in" for ::1 at 2025-08-22 03:11:10 +0600
Processing by SessionsController#create as */*
  Parameters: {"email" => "[FILTERED]", "password" => "[FILTERED]"}
Can't verify CSRF token authenticity.
Completed 422 Unprocessable Content in 5ms (ActiveRecord: 0.0ms (0 queries, 0 cached) | GC: 0.0ms)


  
ActionController::InvalidAuthenticityToken (Can't verify CSRF token authenticity.):
  
actionpack (*******) lib/action_controller/metal/request_forgery_protection.rb:314:in 'ActionController::RequestForgeryProtection::ProtectionMethods::Exception#handle_unverified_request'
actionpack (*******) lib/action_controller/metal/request_forgery_protection.rb:408:in 'ActionController::RequestForgeryProtection#handle_unverified_request'
actionpack (*******) lib/action_controller/metal/request_forgery_protection.rb:397:in 'ActionController::RequestForgeryProtection#verify_authenticity_token'
activesupport (*******) lib/active_support/callbacks.rb:361:in 'block in ActiveSupport::Callbacks::CallTemplate::MethodCall#make_lambda'
activesupport (*******) lib/active_support/callbacks.rb:178:in 'block in ActiveSupport::Callbacks::Filters::Before#call'
actionpack (*******) lib/abstract_controller/callbacks.rb:34:in 'block (2 levels) in <module:Callbacks>'
activesupport (*******) lib/active_support/callbacks.rb:179:in 'ActiveSupport::Callbacks::Filters::Before#call'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'block in ActiveSupport::Callbacks::CallbackSequence#invoke_before'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'Array#each'
activesupport (*******) lib/active_support/callbacks.rb:559:in 'ActiveSupport::Callbacks::CallbackSequence#invoke_before'
activesupport (*******) lib/active_support/callbacks.rb:118:in 'block in ActiveSupport::Callbacks#run_callbacks'
activesupport (*******) lib/active_support/callbacks.rb:140:in 'ActiveSupport::Callbacks#run_callbacks'
actionpack (*******) lib/abstract_controller/callbacks.rb:260:in 'AbstractController::Callbacks#process_action'
actionpack (*******) lib/action_controller/metal/rescue.rb:27:in 'ActionController::Rescue#process_action'
actionpack (*******) lib/action_controller/metal/instrumentation.rb:76:in 'block in ActionController::Instrumentation#process_action'
activesupport (*******) lib/active_support/notifications.rb:210:in 'block in ActiveSupport::Notifications.instrument'
activesupport (*******) lib/active_support/notifications/instrumenter.rb:58:in 'ActiveSupport::Notifications::Instrumenter#instrument'
activesupport (*******) lib/active_support/notifications.rb:210:in 'ActiveSupport::Notifications.instrument'
actionpack (*******) lib/action_controller/metal/instrumentation.rb:75:in 'ActionController::Instrumentation#process_action'
actionpack (*******) lib/action_controller/metal/params_wrapper.rb:259:in 'ActionController::ParamsWrapper#process_action'
activerecord (*******) lib/active_record/railties/controller_runtime.rb:39:in 'ActiveRecord::Railties::ControllerRuntime#process_action'
actionpack (*******) lib/abstract_controller/base.rb:163:in 'AbstractController::Base#process'
actionview (*******) lib/action_view/rendering.rb:40:in 'ActionView::Rendering#process'
actionpack (*******) lib/action_controller/metal.rb:252:in 'ActionController::Metal#dispatch'
actionpack (*******) lib/action_controller/metal.rb:335:in 'ActionController::Metal.dispatch'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:67:in 'ActionDispatch::Routing::RouteSet::Dispatcher#dispatch'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:50:in 'ActionDispatch::Routing::RouteSet::Dispatcher#serve'
actionpack (*******) lib/action_dispatch/journey/router.rb:53:in 'block in ActionDispatch::Journey::Router#serve'
actionpack (*******) lib/action_dispatch/journey/router.rb:133:in 'block in ActionDispatch::Journey::Router#find_routes'
actionpack (*******) lib/action_dispatch/journey/router.rb:126:in 'Array#each'
actionpack (*******) lib/action_dispatch/journey/router.rb:126:in 'ActionDispatch::Journey::Router#find_routes'
actionpack (*******) lib/action_dispatch/journey/router.rb:34:in 'ActionDispatch::Journey::Router#serve'
actionpack (*******) lib/action_dispatch/routing/route_set.rb:908:in 'ActionDispatch::Routing::RouteSet#call'
railties (*******) lib/rails/engine/lazy_route_set.rb:68:in 'Rails::Engine::LazyRouteSet#call'
rack (3.2.0) lib/rack/tempfile_reaper.rb:20:in 'Rack::TempfileReaper#call'
rack (3.2.0) lib/rack/etag.rb:29:in 'Rack::ETag#call'
rack (3.2.0) lib/rack/conditional_get.rb:44:in 'Rack::ConditionalGet#call'
rack (3.2.0) lib/rack/head.rb:15:in 'Rack::Head#call'
actionpack (*******) lib/action_dispatch/http/permissions_policy.rb:38:in 'ActionDispatch::PermissionsPolicy::Middleware#call'
actionpack (*******) lib/action_dispatch/http/content_security_policy.rb:38:in 'ActionDispatch::ContentSecurityPolicy::Middleware#call'
rack-session (2.1.1) lib/rack/session/abstract/id.rb:274:in 'Rack::Session::Abstract::Persisted#context'
rack-session (2.1.1) lib/rack/session/abstract/id.rb:268:in 'Rack::Session::Abstract::Persisted#call'
actionpack (*******) lib/action_dispatch/middleware/cookies.rb:706:in 'ActionDispatch::Cookies#call'
activerecord (*******) lib/active_record/migration.rb:671:in 'ActiveRecord::Migration::CheckPending#call'
actionpack (*******) lib/action_dispatch/middleware/callbacks.rb:31:in 'block in ActionDispatch::Callbacks#call'
activesupport (*******) lib/active_support/callbacks.rb:100:in 'ActiveSupport::Callbacks#run_callbacks'
actionpack (*******) lib/action_dispatch/middleware/callbacks.rb:30:in 'ActionDispatch::Callbacks#call'
actionpack (*******) lib/action_dispatch/middleware/executor.rb:16:in 'ActionDispatch::Executor#call'
actionpack (*******) lib/action_dispatch/middleware/actionable_exceptions.rb:18:in 'ActionDispatch::ActionableExceptions#call'
actionpack (*******) lib/action_dispatch/middleware/debug_exceptions.rb:31:in 'ActionDispatch::DebugExceptions#call'
web-console (4.2.1) lib/web_console/middleware.rb:132:in 'WebConsole::Middleware#call_app'
web-console (4.2.1) lib/web_console/middleware.rb:28:in 'block in WebConsole::Middleware#call'
web-console (4.2.1) lib/web_console/middleware.rb:17:in 'Kernel#catch'
web-console (4.2.1) lib/web_console/middleware.rb:17:in 'WebConsole::Middleware#call'
actionpack (*******) lib/action_dispatch/middleware/show_exceptions.rb:32:in 'ActionDispatch::ShowExceptions#call'
railties (*******) lib/rails/rack/logger.rb:41:in 'Rails::Rack::Logger#call_app'
railties (*******) lib/rails/rack/logger.rb:29:in 'Rails::Rack::Logger#call'
actionpack (*******) lib/action_dispatch/middleware/remote_ip.rb:96:in 'ActionDispatch::RemoteIp#call'
actionpack (*******) lib/action_dispatch/middleware/request_id.rb:34:in 'ActionDispatch::RequestId#call'
rack (3.2.0) lib/rack/method_override.rb:28:in 'Rack::MethodOverride#call'
rack (3.2.0) lib/rack/runtime.rb:24:in 'Rack::Runtime#call'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:61:in 'block in ActionDispatch::ServerTiming#call'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:26:in 'ActionDispatch::ServerTiming::Subscriber#collect_events'
actionpack (*******) lib/action_dispatch/middleware/server_timing.rb:60:in 'ActionDispatch::ServerTiming#call'
actionpack (*******) lib/action_dispatch/middleware/executor.rb:16:in 'ActionDispatch::Executor#call'
actionpack (*******) lib/action_dispatch/middleware/static.rb:27:in 'ActionDispatch::Static#call'
rack (3.2.0) lib/rack/sendfile.rb:114:in 'Rack::Sendfile#call'
actionpack (*******) lib/action_dispatch/middleware/host_authorization.rb:143:in 'ActionDispatch::HostAuthorization#call'
railties (*******) lib/rails/engine.rb:535:in 'Rails::Engine#call'
puma (6.6.1) lib/puma/configuration.rb:279:in 'Puma::Configuration::ConfigMiddleware#call'
puma (6.6.1) lib/puma/request.rb:99:in 'block in Puma::Request#handle_request'
puma (6.6.1) lib/puma/thread_pool.rb:390:in 'Puma::ThreadPool#with_force_shutdown'
puma (6.6.1) lib/puma/request.rb:98:in 'Puma::Request#handle_request'
puma (6.6.1) lib/puma/server.rb:472:in 'Puma::Server#process_client'
puma (6.6.1) lib/puma/server.rb:254:in 'block in Puma::Server#run'
puma (6.6.1) lib/puma/thread_pool.rb:167:in 'block in Puma::ThreadPool#spawn_thread'
