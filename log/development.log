  [1m[35mSQL (0.1ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (157.9ms)[0m  [1m[35mCREATE DATABASE "cloudvision_app_development" ENCODING = 'unicode' /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (71.1ms)[0m  [1m[35mCREATE DATABASE "cloudvision_app_test" ENCODING = 'unicode' /*application='CloudvisionApp'*/[0m
  [1m[35m (4.9ms)[0m  [1m[35mCREATE TABLE "schema_migrations" ("version" character varying NOT NULL PRIMARY KEY) /*application='CloudvisionApp'*/[0m
  [1m[35m (2.5ms)[0m  [1m[35mCREATE TABLE "ar_internal_metadata" ("key" character varying NOT NULL PRIMARY KEY, "value" character varying, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.6ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE ActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
  [1m[36mCACHE ActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
  [1m[36mCACHE ActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
  [1m[36mCACHE ActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
  [1m[35m (0.2ms)[0m  [1m[34mSELECT pg_try_advisory_lock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.9ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.8ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Create (0.8ms)[0m  [1m[32mINSERT INTO "ar_internal_metadata" ("key", "value", "created_at", "updated_at") VALUES ('environment', 'development', '2025-08-21 17:51:39.092018', '2025-08-21 17:51:39.092023') RETURNING "key" /*application='CloudvisionApp'*/[0m
Migrating to DeviseCreateUsers (20250821175014)
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[35m (4.6ms)[0m  [1m[35mCREATE TABLE "users" ("id" bigserial primary key, "email" character varying DEFAULT '' NOT NULL, "encrypted_password" character varying DEFAULT '' NOT NULL, "reset_password_token" character varying, "reset_password_sent_at" timestamp(6), "remember_created_at" timestamp(6), "first_name" character varying, "last_name" character varying, "bio" text, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.7ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_users_on_email" ON "users" ("email") /*application='CloudvisionApp'*/[0m
  [1m[35m (1.2ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_users_on_reset_password_token" ON "users" ("reset_password_token") /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (1.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250821175014') RETURNING "version" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.9ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
Migrating to CreateCategories (20250821175020)
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[35m (5.6ms)[0m  [1m[35mCREATE TABLE "categories" ("id" bigserial primary key, "name" character varying, "description" text, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.4ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250821175020') RETURNING "version" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.6ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
Migrating to CreateEntities (20250821175026)
  [1m[36mTRANSACTION (1.3ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[35m (6.5ms)[0m  [1m[35mCREATE TABLE "entities" ("id" bigserial primary key, "name" character varying NOT NULL, "description" text, "address" text, "phone" character varying, "website" character varying, "email" character varying, "latitude" decimal(10,6), "longitude" decimal(10,6), "active" boolean DEFAULT TRUE, "category_id" bigint NOT NULL, "user_id" bigint NOT NULL, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL, CONSTRAINT "fk_rails_3b99dd0cd1"
FOREIGN KEY ("category_id")
  REFERENCES "categories" ("id")
, CONSTRAINT "fk_rails_71e168c975"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
) /*application='CloudvisionApp'*/[0m
  [1m[35m (1.2ms)[0m  [1m[35mCREATE INDEX "index_entities_on_category_id" ON "entities" ("category_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (1.5ms)[0m  [1m[35mCREATE INDEX "index_entities_on_user_id" ON "entities" ("user_id") /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250821175026') RETURNING "version" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
Migrating to CreateReviews (20250821175031)
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[35m (6.3ms)[0m  [1m[35mCREATE TABLE "reviews" ("id" bigserial primary key, "rating" integer NOT NULL, "comment" text, "user_id" bigint NOT NULL, "entity_id" bigint NOT NULL, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL, CONSTRAINT "fk_rails_74a66bd6c5"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
, CONSTRAINT "fk_rails_239809e095"
FOREIGN KEY ("entity_id")
  REFERENCES "entities" ("id")
) /*application='CloudvisionApp'*/[0m
  [1m[35m (1.3ms)[0m  [1m[35mCREATE INDEX "index_reviews_on_user_id" ON "reviews" ("user_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.9ms)[0m  [1m[35mCREATE INDEX "index_reviews_on_entity_id" ON "reviews" ("entity_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.8ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_reviews_on_user_id_and_entity_id" ON "reviews" ("user_id", "entity_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mALTER TABLE "reviews" ADD CONSTRAINT rating_range_check CHECK (rating >= 1 AND rating <= 5) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.3ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250821175031') RETURNING "version" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[34mSELECT pg_advisory_unlock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.3ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.8ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (1.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Restaurants' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (0.4ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Restaurants') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.7ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Restaurants', 'Dining establishments, cafes, and food services', '2025-08-21 17:56:53.026530', '2025-08-21 17:56:53.026530') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.6ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Shops' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.7ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Shops') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.4ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Shops', 'Retail stores and shopping centers', '2025-08-21 17:56:53.044376', '2025-08-21 17:56:53.044376') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.6ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.6ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Entertainment' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.6ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Entertainment') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (1.4ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Entertainment', 'Cinemas, theaters, and entertainment venues', '2025-08-21 17:56:53.074519', '2025-08-21 17:56:53.074519') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.9ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Hotels' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.2ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Hotels') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.6ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Hotels', 'Hotels, motels, and accommodation services', '2025-08-21 17:56:53.095611', '2025-08-21 17:56:53.095611') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.5ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Services' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.2ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Services') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.4ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Services', 'Professional and personal services', '2025-08-21 17:56:53.110847', '2025-08-21 17:56:53.110847') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Health & Fitness' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.2ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Health & Fitness') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.4ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Health & Fitness', 'Gyms, spas, and health services', '2025-08-21 17:56:53.117819', '2025-08-21 17:56:53.117819') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Education' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.1ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Education') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.3ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Education', 'Schools, universities, and educational institutions', '2025-08-21 17:56:53.128453', '2025-08-21 17:56:53.128453') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Automotive' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.2ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Automotive') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.5ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Automotive', 'Car dealerships, repair shops, and automotive services', '2025-08-21 17:56:53.138521', '2025-08-21 17:56:53.138521') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (1.0ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mUser Exists? (0.6ms)[0m  [1m[34mSELECT 1 AS one FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Create (1.0ms)[0m  [1m[32mINSERT INTO "users" ("email", "encrypted_password", "reset_password_token", "reset_password_sent_at", "remember_created_at", "first_name", "last_name", "bio", "created_at", "updated_at") VALUES ('<EMAIL>', '$2a$12$LKph3yiDwSs.ZJGHAIAam.BlPcGCmjXhdhXXPqGjcQ5QbOxJUT2RG', NULL, NULL, NULL, 'John', 'Doe', 'Food enthusiast and local explorer', '2025-08-21 17:56:55.079293', '2025-08-21 17:56:55.079293') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.9ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.4ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mUser Exists? (1.4ms)[0m  [1m[34mSELECT 1 AS one FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Create (0.4ms)[0m  [1m[32mINSERT INTO "users" ("email", "encrypted_password", "reset_password_token", "reset_password_sent_at", "remember_created_at", "first_name", "last_name", "bio", "created_at", "updated_at") VALUES ('<EMAIL>', '$2a$12$08rh.njf/bhqmc6kKVskluHDzrO8r4yr6VKPdsP0yP2wrmnzSRZt2', NULL, NULL, NULL, 'Jane', 'Smith', 'Travel blogger and restaurant critic', '2025-08-21 17:56:55.386487', '2025-08-21 17:56:55.386487') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.7ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.4ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mUser Exists? (1.6ms)[0m  [1m[34mSELECT 1 AS one FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Create (0.3ms)[0m  [1m[32mINSERT INTO "users" ("email", "encrypted_password", "reset_password_token", "reset_password_sent_at", "remember_created_at", "first_name", "last_name", "bio", "created_at", "updated_at") VALUES ('<EMAIL>', '$2a$12$F04nN73gR9NmCp5UfWhCF.JVyZEhohi7ZupGMMzBHI.EU89tU6L3u', NULL, NULL, NULL, 'Mike', 'Johnson', 'Local business owner', '2025-08-21 17:56:55.687823', '2025-08-21 17:56:55.687823') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Restaurants' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.7ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (2.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'The Gourmet Corner' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (2.1ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('The Gourmet Corner', 'Fine dining restaurant with contemporary cuisine and excellent wine selection.', '123 Main Street, Downtown', '(*************', 'https://gourmetcorner.com', '<EMAIL>', NULL, NULL, TRUE, 1, 1, '2025-08-21 17:56:55.757727', '2025-08-21 17:56:55.757727') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Shops' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.1ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Tech Haven Electronics' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (1.1ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('Tech Haven Electronics', 'Your one-stop shop for the latest electronics, gadgets, and tech accessories.', '456 Tech Avenue, Silicon Valley', '(*************', 'https://techhaven.com', '<EMAIL>', NULL, NULL, TRUE, 2, 1, '2025-08-21 17:56:55.770341', '2025-08-21 17:56:55.770341') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Entertainment' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Starlight Cinema' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (1.3ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('Starlight Cinema', 'Modern movie theater with IMAX screens and premium seating options.', '789 Entertainment Blvd, City Center', '(*************', 'https://starlightcinema.com', '<EMAIL>', NULL, NULL, TRUE, 3, 1, '2025-08-21 17:56:55.782453', '2025-08-21 17:56:55.782453') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Hotels' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Grand Plaza Hotel' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (1.3ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('Grand Plaza Hotel', 'Luxury hotel in the heart of the city with world-class amenities.', '321 Hotel Row, Business District', '(*************', 'https://grandplaza.com', '<EMAIL>', NULL, NULL, TRUE, 4, 1, '2025-08-21 17:56:55.792809', '2025-08-21 17:56:55.792809') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.9ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Health & Fitness' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.4ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'FitLife Gym' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (1.3ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('FitLife Gym', 'State-of-the-art fitness center with personal training and group classes.', '654 Fitness Street, Health District', '(*************', 'https://fitlifegym.com', '<EMAIL>', NULL, NULL, TRUE, 6, 1, '2025-08-21 17:56:55.806885', '2025-08-21 17:56:55.806885') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.1ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.9ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 1 AND "reviews"."user_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.4ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 2 AND "reviews"."entity_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (1.7ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (5, 'Very satisfied with the service.', 2, 1, '2025-08-21 17:56:55.869998', '2025-08-21 17:56:55.869998') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[31mROLLBACK /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (1.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (1.1ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Restaurants' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Shops' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.6ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Entertainment' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Hotels' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Services' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Health & Fitness' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Education' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.7ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Automotive' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (1.5ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.4ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (1.7ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Restaurants' LIMIT 1[0m
  [1m[36mUser Load (0.8ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (1.1ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'The Gourmet Corner' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Shops' LIMIT 1[0m
  [1m[36mCACHE User Load (0.0ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1[0m
  [1m[36mEntity Load (0.8ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Tech Haven Electronics' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (0.0ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Entertainment' LIMIT 1[0m
  [1m[36mCACHE User Load (0.0ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1[0m
  [1m[36mEntity Load (0.5ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Starlight Cinema' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (0.0ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Hotels' LIMIT 1[0m
  [1m[36mCACHE User Load (0.0ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1[0m
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Grand Plaza Hotel' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (0.0ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Health & Fitness' LIMIT 1[0m
  [1m[36mCACHE User Load (0.0ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1[0m
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'FitLife Gym' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.7ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 1 AND "reviews"."user_id" = 3 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (2.6ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 3 AND "reviews"."entity_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (1.7ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (4, 'Great experience! Highly recommended.', 3, 1, '2025-08-21 17:58:18.783895', '2025-08-21 17:58:18.783895') RETURNING "id" /*application='CloudvisionApp'*/[0m
  Rendered reviews/_review.html.erb (Duration: 78.1ms | GC: 0.0ms)
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[31mROLLBACK /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (1.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (1.0ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Restaurants' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Shops' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.7ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Entertainment' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.6ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Hotels' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Services' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Health & Fitness' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Education' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Automotive' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (1.3ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.6ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.4ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (1.6ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Restaurants' LIMIT 1[0m
  [1m[36mUser Load (1.1ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (1.1ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'The Gourmet Corner' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Shops' LIMIT 1[0m
  [1m[36mCACHE User Load (0.0ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1[0m
  [1m[36mEntity Load (0.5ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Tech Haven Electronics' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (0.0ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Entertainment' LIMIT 1[0m
  [1m[36mCACHE User Load (0.0ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1[0m
  [1m[36mEntity Load (0.4ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Starlight Cinema' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (0.0ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Hotels' LIMIT 1[0m
  [1m[36mCACHE User Load (0.0ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1[0m
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Grand Plaza Hotel' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCACHE Category Load (0.0ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Health & Fitness' LIMIT 1[0m
  [1m[36mCACHE User Load (0.0ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1[0m
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'FitLife Gym' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.4ms)[0m  [1m[34mSELECT "entities".* FROM "entities" /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.3ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.5ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 1 AND "reviews"."user_id" = 3 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (2.7ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 3 AND "reviews"."entity_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (1.4ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (3, 'Great experience! Highly recommended.', 3, 1, '2025-08-21 17:59:02.422722', '2025-08-21 17:59:02.422722') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (1.6ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.4ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 2 AND "reviews"."user_id" = 3 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.1ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 3 AND "reviews"."entity_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.8ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (3, 'Great experience! Highly recommended.', 3, 2, '2025-08-21 17:59:02.448498', '2025-08-21 17:59:02.448498') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 2 AND "reviews"."user_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.0ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 2 AND "reviews"."entity_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.7ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (4, 'Very satisfied with the service.', 2, 2, '2025-08-21 17:59:02.458367', '2025-08-21 17:59:02.458367') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 2 AND "reviews"."user_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.0ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 1 AND "reviews"."entity_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.6ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (4, 'Good service and quality. Will visit again.', 1, 2, '2025-08-21 17:59:02.473952', '2025-08-21 17:59:02.473952') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.3ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 3 AND "reviews"."user_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 1 AND "reviews"."entity_id" = 3 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.7ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (3, 'Professional service and great atmosphere.', 1, 3, '2025-08-21 17:59:02.489862', '2025-08-21 17:59:02.489862') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 4 AND "reviews"."user_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (6.5ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 1 AND "reviews"."entity_id" = 4 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.6ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (5, 'Outstanding quality and friendly staff.', 1, 4, '2025-08-21 17:59:02.515435', '2025-08-21 17:59:02.515435') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.6ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.4ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 4 AND "reviews"."user_id" = 3 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.0ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 3 AND "reviews"."entity_id" = 4 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.3ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (4, 'Amazing place! Love coming here.', 3, 4, '2025-08-21 17:59:02.523269', '2025-08-21 17:59:02.523269') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 5 AND "reviews"."user_id" = 3 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.1ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 3 AND "reviews"."entity_id" = 5 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.4ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (3, 'Good service and quality. Will visit again.', 3, 5, '2025-08-21 17:59:02.532266', '2025-08-21 17:59:02.532266') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.4ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 5 AND "reviews"."user_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.8ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 1 AND "reviews"."entity_id" = 5 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.4ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (5, 'Very satisfied with the service.', 1, 5, '2025-08-21 17:59:02.537607', '2025-08-21 17:59:02.537607') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Count (0.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "categories" /*application='CloudvisionApp'*/[0m
  [1m[36mUser Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" /*application='CloudvisionApp'*/[0m
Started HEAD "/" for ::1 at 2025-08-22 00:00:45 +0600
  [1m[36mActiveRecord::SchemaMigration Load (1.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
Processing by HomeController#index as */*
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (2.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (2.7ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.8ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (8.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.7ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.7ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (1.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (1.0ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.6ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.5ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.5ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.7ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.4ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.2ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (1.6ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (1, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.6ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 299.9ms | GC: 0.0ms)
  Rendered layout layouts/application.html.erb (Duration: 318.1ms | GC: 0.0ms)
Completed 200 OK in 479ms (Views: 284.5ms | ActiveRecord: 53.8ms (47 queries, 20 cached) | GC: 0.0ms)


Started GET "/" for ::1 at 2025-08-22 00:01:00 +0600
Processing by HomeController#index as HTML
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.4ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (1.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.3ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (1.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.4ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (1.0ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.5ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (0.3ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (1, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (1.0ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 113.9ms | GC: 17.1ms)
  Rendered layout layouts/application.html.erb (Duration: 121.1ms | GC: 19.8ms)
Completed 200 OK in 162ms (Views: 112.4ms | ActiveRecord: 10.0ms (47 queries, 20 cached) | GC: 19.8ms)


Started GET "/assets/application-8b441ae0.css" for ::1 at 2025-08-22 00:01:00 +0600
Started GET "/assets/turbo.min-3a2e143f.js" for ::1 at 2025-08-22 00:01:00 +0600
Started GET "/assets/stimulus.min-4b1e420e.js" for ::1 at 2025-08-22 00:01:00 +0600
Started GET "/assets/controllers/flash_controller-21fc149c.js" for ::1 at 2025-08-22 00:01:00 +0600
Started GET "/assets/controllers/hello_controller-708796bd.js" for ::1 at 2025-08-22 00:01:00 +0600
Started GET "/assets/controllers/index-ee64e1f1.js" for ::1 at 2025-08-22 00:01:00 +0600
Started GET "/assets/controllers/star_rating_controller-ca4daf08.js" for ::1 at 2025-08-22 00:01:00 +0600
Started GET "/assets/application-bfcdf840.js" for ::1 at 2025-08-22 00:01:01 +0600
Started GET "/assets/stimulus-loading-1fc53fe7.js" for ::1 at 2025-08-22 00:01:01 +0600
Started GET "/assets/controllers/application-3affb389.js" for ::1 at 2025-08-22 00:01:01 +0600
Started GET "/" for ::1 at 2025-08-22 00:01:25 +0600
Processing by HomeController#index as HTML
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.4ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.2ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.1ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.2ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (1, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 43.2ms | GC: 2.3ms)
  Rendered layout layouts/application.html.erb (Duration: 70.8ms | GC: 6.1ms)
Completed 200 OK in 103ms (Views: 65.8ms | ActiveRecord: 5.3ms (47 queries, 20 cached) | GC: 6.1ms)


Started GET "/categories/8" for ::1 at 2025-08-22 00:01:31 +0600
Processing by CategoriesController#show as HTML
  Parameters: {"id" => "8"}
  [1m[36mCategory Load (0.7ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" = 8 LIMIT 1 /*action='show',application='CloudvisionApp',controller='categories'*/[0m
  ↳ app/controllers/categories_controller.rb:15:in 'CategoriesController#set_category'
  Rendering layout layouts/application.html.erb
  Rendering categories/show.html.erb within layouts/application
  Rendered categories/show.html.erb within layouts/application (Duration: 7.3ms | GC: 0.0ms)
  Rendered layout layouts/application.html.erb (Duration: 10.7ms | GC: 0.0ms)
Completed 200 OK in 58ms (Views: 14.2ms | ActiveRecord: 0.7ms (1 query, 0 cached) | GC: 0.0ms)


  [1m[36mCACHE ActiveRecord::SchemaMigration Load (0.8ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.4ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (1.0ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.3ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.2ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (45.4ms)[0m  [1m[35mDROP DATABASE IF EXISTS "cloudvision_app_development" /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (1.1ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (48.4ms)[0m  [1m[35mDROP DATABASE IF EXISTS "cloudvision_app_test" /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (60.9ms)[0m  [1m[35mCREATE DATABASE "cloudvision_app_development" ENCODING = 'unicode' /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (84.2ms)[0m  [1m[35mCREATE DATABASE "cloudvision_app_test" ENCODING = 'unicode' /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mCREATE EXTENSION IF NOT EXISTS "plpgsql" SCHEMA pg_catalog /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE IF EXISTS "categories" CASCADE /*application='CloudvisionApp'*/[0m
  [1m[35m (2.6ms)[0m  [1m[35mCREATE TABLE "categories" ("id" bigserial primary key, "name" character varying, "description" text, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mDROP TABLE IF EXISTS "entities" CASCADE /*application='CloudvisionApp'*/[0m
  [1m[35m (1.5ms)[0m  [1m[35mCREATE TABLE "entities" ("id" bigserial primary key, "name" character varying NOT NULL, "description" text, "address" text, "phone" character varying, "website" character varying, "email" character varying, "latitude" decimal(10,6), "longitude" decimal(10,6), "active" boolean DEFAULT TRUE, "category_id" bigint NOT NULL, "user_id" bigint NOT NULL, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_entities_on_category_id" ON "entities" ("category_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_entities_on_user_id" ON "entities" ("user_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE IF EXISTS "reviews" CASCADE /*application='CloudvisionApp'*/[0m
  [1m[35m (1.3ms)[0m  [1m[35mCREATE TABLE "reviews" ("id" bigserial primary key, "rating" integer NOT NULL, "comment" text, "user_id" bigint NOT NULL, "entity_id" bigint NOT NULL, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL, CONSTRAINT rating_range_check CHECK (rating >= 1 AND rating <= 5)) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_reviews_on_entity_id" ON "reviews" ("entity_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_reviews_on_user_id_and_entity_id" ON "reviews" ("user_id", "entity_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_reviews_on_user_id" ON "reviews" ("user_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "users" CASCADE /*application='CloudvisionApp'*/[0m
  [1m[35m (1.3ms)[0m  [1m[35mCREATE TABLE "users" ("id" bigserial primary key, "email" character varying DEFAULT '' NOT NULL, "encrypted_password" character varying DEFAULT '' NOT NULL, "reset_password_token" character varying, "reset_password_sent_at" timestamp(6), "remember_created_at" timestamp(6), "first_name" character varying, "last_name" character varying, "bio" text, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_users_on_email" ON "users" ("email") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_users_on_reset_password_token" ON "users" ("reset_password_token") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.9ms)[0m  [1m[35mALTER TABLE "entities" ADD CONSTRAINT "fk_rails_3b99dd0cd1"
FOREIGN KEY ("category_id")
  REFERENCES "categories" ("id")
 /*application='CloudvisionApp'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mALTER TABLE "entities" ADD CONSTRAINT "fk_rails_71e168c975"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
 /*application='CloudvisionApp'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mALTER TABLE "reviews" ADD CONSTRAINT "fk_rails_239809e095"
FOREIGN KEY ("entity_id")
  REFERENCES "entities" ("id")
 /*application='CloudvisionApp'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mALTER TABLE "reviews" ADD CONSTRAINT "fk_rails_74a66bd6c5"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
 /*application='CloudvisionApp'*/[0m
  [1m[35m (1.0ms)[0m  [1m[35mCREATE TABLE "schema_migrations" ("version" character varying NOT NULL PRIMARY KEY) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" (version) VALUES (20250821175031) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" (version) VALUES
(20250821175026),
(20250821175020); /*application='CloudvisionApp'*/[0m
  [1m[35m (0.9ms)[0m  [1m[35mCREATE TABLE "ar_internal_metadata" ("key" character varying NOT NULL PRIMARY KEY, "value" character varying, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.2ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Create (0.2ms)[0m  [1m[32mINSERT INTO "ar_internal_metadata" ("key", "value", "created_at", "updated_at") VALUES ('environment', 'development', '2025-08-21 18:12:45.778313', '2025-08-21 18:12:45.778314') RETURNING "key" /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.1ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.0ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'schema_sha1' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Create (0.1ms)[0m  [1m[32mINSERT INTO "ar_internal_metadata" ("key", "value", "created_at", "updated_at") VALUES ('schema_sha1', '195f61e27c90a09af4100253777d3720bbf39ac5', '2025-08-21 18:12:45.783352', '2025-08-21 18:12:45.783353') RETURNING "key" /*application='CloudvisionApp'*/[0m
  [1m[35m (0.0ms)[0m  [1m[34mSELECT pg_try_advisory_lock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.1ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
Migrating to CreateUsers (20250821181218)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[35m (2.3ms)[0m  [1m[35mCREATE TABLE "users" ("id" bigserial primary key, "email" character varying NOT NULL, "password_digest" character varying NOT NULL, "first_name" character varying NOT NULL, "last_name" character varying NOT NULL, "bio" text, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.5ms)[0m  [1m[31mROLLBACK /*application='CloudvisionApp'*/[0m
  [1m[35m (0.0ms)[0m  [1m[34mSELECT pg_advisory_unlock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.3ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.4ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.1ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.1ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (96.9ms)[0m  [1m[35mDROP DATABASE IF EXISTS "cloudvision_app_development" /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (1.1ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (34.7ms)[0m  [1m[35mDROP DATABASE IF EXISTS "cloudvision_app_test" /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (56.2ms)[0m  [1m[35mCREATE DATABASE "cloudvision_app_development" ENCODING = 'unicode' /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mSET search_path TO public /*application='CloudvisionApp'*/[0m
  [1m[35m (53.6ms)[0m  [1m[35mCREATE DATABASE "cloudvision_app_test" ENCODING = 'unicode' /*application='CloudvisionApp'*/[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mCREATE EXTENSION IF NOT EXISTS "plpgsql" SCHEMA pg_catalog /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE IF EXISTS "categories" CASCADE /*application='CloudvisionApp'*/[0m
  [1m[35m (2.8ms)[0m  [1m[35mCREATE TABLE "categories" ("id" bigserial primary key, "name" character varying, "description" text, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE IF EXISTS "entities" CASCADE /*application='CloudvisionApp'*/[0m
  [1m[35m (1.2ms)[0m  [1m[35mCREATE TABLE "entities" ("id" bigserial primary key, "name" character varying NOT NULL, "description" text, "address" text, "phone" character varying, "website" character varying, "email" character varying, "latitude" decimal(10,6), "longitude" decimal(10,6), "active" boolean DEFAULT TRUE, "category_id" bigint NOT NULL, "user_id" bigint NOT NULL, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_entities_on_category_id" ON "entities" ("category_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_entities_on_user_id" ON "entities" ("user_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "reviews" CASCADE /*application='CloudvisionApp'*/[0m
  [1m[35m (1.4ms)[0m  [1m[35mCREATE TABLE "reviews" ("id" bigserial primary key, "rating" integer NOT NULL, "comment" text, "user_id" bigint NOT NULL, "entity_id" bigint NOT NULL, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL, CONSTRAINT rating_range_check CHECK (rating >= 1 AND rating <= 5)) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_reviews_on_entity_id" ON "reviews" ("entity_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_reviews_on_user_id_and_entity_id" ON "reviews" ("user_id", "entity_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_reviews_on_user_id" ON "reviews" ("user_id") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "users" CASCADE /*application='CloudvisionApp'*/[0m
  [1m[35m (1.1ms)[0m  [1m[35mCREATE TABLE "users" ("id" bigserial primary key, "email" character varying DEFAULT '' NOT NULL, "encrypted_password" character varying DEFAULT '' NOT NULL, "reset_password_token" character varying, "reset_password_sent_at" timestamp(6), "remember_created_at" timestamp(6), "first_name" character varying, "last_name" character varying, "bio" text, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_users_on_email" ON "users" ("email") /*application='CloudvisionApp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_users_on_reset_password_token" ON "users" ("reset_password_token") /*application='CloudvisionApp'*/[0m
  [1m[35m (1.1ms)[0m  [1m[35mALTER TABLE "entities" ADD CONSTRAINT "fk_rails_3b99dd0cd1"
FOREIGN KEY ("category_id")
  REFERENCES "categories" ("id")
 /*application='CloudvisionApp'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mALTER TABLE "entities" ADD CONSTRAINT "fk_rails_71e168c975"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
 /*application='CloudvisionApp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mALTER TABLE "reviews" ADD CONSTRAINT "fk_rails_239809e095"
FOREIGN KEY ("entity_id")
  REFERENCES "entities" ("id")
 /*application='CloudvisionApp'*/[0m
  [1m[35m (0.7ms)[0m  [1m[35mALTER TABLE "reviews" ADD CONSTRAINT "fk_rails_74a66bd6c5"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
 /*application='CloudvisionApp'*/[0m
  [1m[35m (1.1ms)[0m  [1m[35mCREATE TABLE "schema_migrations" ("version" character varying NOT NULL PRIMARY KEY) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" (version) VALUES (20250821175031) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" (version) VALUES
(20250821175026),
(20250821175020); /*application='CloudvisionApp'*/[0m
  [1m[35m (0.9ms)[0m  [1m[35mCREATE TABLE "ar_internal_metadata" ("key" character varying NOT NULL PRIMARY KEY, "value" character varying, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.3ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Create (0.2ms)[0m  [1m[32mINSERT INTO "ar_internal_metadata" ("key", "value", "created_at", "updated_at") VALUES ('environment', 'development', '2025-08-21 18:22:22.381522', '2025-08-21 18:22:22.381523') RETURNING "key" /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.1ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.0ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'schema_sha1' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Create (0.1ms)[0m  [1m[32mINSERT INTO "ar_internal_metadata" ("key", "value", "created_at", "updated_at") VALUES ('schema_sha1', '195f61e27c90a09af4100253777d3720bbf39ac5', '2025-08-21 18:22:22.387082', '2025-08-21 18:22:22.387082') RETURNING "key" /*application='CloudvisionApp'*/[0m
  [1m[35m (0.0ms)[0m  [1m[34mSELECT pg_try_advisory_lock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.1ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
Migrating to CreateUsers (20250821181218)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[35m (2.5ms)[0m  [1m[35mCREATE TABLE "users" ("id" bigserial primary key, "email" character varying NOT NULL, "password_digest" character varying NOT NULL, "first_name" character varying NOT NULL, "last_name" character varying NOT NULL, "bio" text, "created_at" timestamp(6) NOT NULL, "updated_at" timestamp(6) NOT NULL) /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.5ms)[0m  [1m[31mROLLBACK /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[34mSELECT pg_advisory_unlock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[34mSELECT pg_try_advisory_lock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.9ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.5ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
Migrating to ConvertUsersToRailsAuth (20250821182423)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[35m (5.0ms)[0m  [1m[35mALTER TABLE "users" DROP COLUMN "encrypted_password" /*application='CloudvisionApp'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mALTER TABLE "users" DROP COLUMN "reset_password_token" /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mALTER TABLE "users" DROP COLUMN "reset_password_sent_at" /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mALTER TABLE "users" DROP COLUMN "remember_created_at" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[31mROLLBACK /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[34mSELECT pg_advisory_unlock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[34mSELECT pg_try_advisory_lock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (1.9ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.9ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
Migrating to ConvertUsersToRailsAuth (20250821182423)
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[35m (6.6ms)[0m  [1m[35mALTER TABLE "users" DROP COLUMN "encrypted_password" /*application='CloudvisionApp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mALTER TABLE "users" DROP COLUMN "reset_password_token" /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mALTER TABLE "users" DROP COLUMN "reset_password_sent_at" /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mALTER TABLE "users" DROP COLUMN "remember_created_at" /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mALTER TABLE "users" ADD "password_digest" character varying NOT NULL /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mALTER TABLE "users" ALTER COLUMN "first_name" SET NOT NULL /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mALTER TABLE "users" ALTER COLUMN "last_name" SET NOT NULL /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250821182423') RETURNING "version" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.6ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[34mSELECT pg_advisory_unlock(5380250951971905660) /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (1.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (1.4ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (1.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Restaurants' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (0.4ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Restaurants') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (1.4ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Restaurants', 'Dining establishments, cafes, and food services', '2025-08-21 18:28:34.814642', '2025-08-21 18:28:34.814642') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.7ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Shops' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.2ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Shops') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.4ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Shops', 'Retail stores and shopping centers', '2025-08-21 18:28:34.847677', '2025-08-21 18:28:34.847677') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.8ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.6ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Entertainment' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.4ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Entertainment') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (1.4ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Entertainment', 'Cinemas, theaters, and entertainment venues', '2025-08-21 18:28:34.880008', '2025-08-21 18:28:34.880008') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.5ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Hotels' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.5ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Hotels') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.6ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Hotels', 'Hotels, motels, and accommodation services', '2025-08-21 18:28:34.902926', '2025-08-21 18:28:34.902926') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.5ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Services' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.4ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Services') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.4ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Services', 'Professional and personal services', '2025-08-21 18:28:34.918071', '2025-08-21 18:28:34.918071') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Health & Fitness' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.1ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Health & Fitness') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.2ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Health & Fitness', 'Gyms, spas, and health services', '2025-08-21 18:28:34.926981', '2025-08-21 18:28:34.926981') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Education' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (0.9ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Education') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.2ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Education', 'Schools, universities, and educational institutions', '2025-08-21 18:28:34.938116', '2025-08-21 18:28:34.938116') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Automotive' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Exists? (1.8ms)[0m  [1m[34mSELECT 1 AS one FROM "categories" WHERE LOWER("categories"."name") = LOWER('Automotive') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Create (0.3ms)[0m  [1m[32mINSERT INTO "categories" ("name", "description", "created_at", "updated_at") VALUES ('Automotive', 'Car dealerships, repair shops, and automotive services', '2025-08-21 18:28:34.950118', '2025-08-21 18:28:34.950118') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (1.0ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mUser Exists? (0.5ms)[0m  [1m[34mSELECT 1 AS one FROM "users" WHERE LOWER("users"."email") = LOWER('<EMAIL>') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Create (1.6ms)[0m  [1m[32mINSERT INTO "users" ("email", "first_name", "last_name", "bio", "created_at", "updated_at", "password_digest") VALUES ('<EMAIL>', 'John', 'Doe', 'Food enthusiast and local explorer', '2025-08-21 18:28:35.331194', '2025-08-21 18:28:35.331194', '$2a$12$oVSoKT1mt.dq5uDCf/DvLexytGgESetf1Zgp2CF4z7oHvT85VVVmu') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.5ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mUser Exists? (1.6ms)[0m  [1m[34mSELECT 1 AS one FROM "users" WHERE LOWER("users"."email") = LOWER('<EMAIL>') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Create (0.3ms)[0m  [1m[32mINSERT INTO "users" ("email", "first_name", "last_name", "bio", "created_at", "updated_at", "password_digest") VALUES ('<EMAIL>', 'Jane', 'Smith', 'Travel blogger and restaurant critic', '2025-08-21 18:28:35.638585', '2025-08-21 18:28:35.638585', '$2a$12$YvDcjmlPDIRfikg4gY5znOXCM7qyJBq5q.qHbD055aGbZ2pAZ9BXK') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.7ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mUser Exists? (1.7ms)[0m  [1m[34mSELECT 1 AS one FROM "users" WHERE LOWER("users"."email") = LOWER('<EMAIL>') LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Create (0.4ms)[0m  [1m[32mINSERT INTO "users" ("email", "first_name", "last_name", "bio", "created_at", "updated_at", "password_digest") VALUES ('<EMAIL>', 'Mike', 'Johnson', 'Local business owner', '2025-08-21 18:28:35.940023', '2025-08-21 18:28:35.940023', '$2a$12$uKK715ZCQG1xuq81AW6TL.0yzTEMNMjMMqdpvIPiF56ESpzkQSXU6') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.5ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Restaurants' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.8ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.8ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'The Gourmet Corner' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (2.4ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('The Gourmet Corner', 'Fine dining restaurant with contemporary cuisine and excellent wine selection.', '123 Main Street, Downtown', '(*************', 'https://gourmetcorner.com', '<EMAIL>', NULL, NULL, TRUE, 1, 1, '2025-08-21 18:28:36.000688', '2025-08-21 18:28:36.000688') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.5ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Shops' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Tech Haven Electronics' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (4.8ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('Tech Haven Electronics', 'Your one-stop shop for the latest electronics, gadgets, and tech accessories.', '456 Tech Avenue, Silicon Valley', '(*************', 'https://techhaven.com', '<EMAIL>', NULL, NULL, TRUE, 2, 1, '2025-08-21 18:28:36.013977', '2025-08-21 18:28:36.013977') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.5ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Entertainment' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.3ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Starlight Cinema' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (1.2ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('Starlight Cinema', 'Modern movie theater with IMAX screens and premium seating options.', '789 Entertainment Blvd, City Center', '(*************', 'https://starlightcinema.com', '<EMAIL>', NULL, NULL, TRUE, 3, 1, '2025-08-21 18:28:36.030483', '2025-08-21 18:28:36.030483') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Hotels' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.3ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'Grand Plaza Hotel' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (1.6ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('Grand Plaza Hotel', 'Luxury hotel in the heart of the city with world-class amenities.', '321 Hotel Row, Business District', '(*************', 'https://grandplaza.com', '<EMAIL>', NULL, NULL, TRUE, 4, 1, '2025-08-21 18:28:36.040875', '2025-08-21 18:28:36.040875') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."name" = 'Health & Fitness' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" ORDER BY "users"."id" ASC LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."name" = 'FitLife Gym' LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Create (1.2ms)[0m  [1m[32mINSERT INTO "entities" ("name", "description", "address", "phone", "website", "email", "latitude", "longitude", "active", "category_id", "user_id", "created_at", "updated_at") VALUES ('FitLife Gym', 'State-of-the-art fitness center with personal training and group classes.', '654 Fitness Street, Health District', '(*************', 'https://fitlifegym.com', '<EMAIL>', NULL, NULL, TRUE, 6, 1, '2025-08-21 18:28:36.051049', '2025-08-21 18:28:36.051049') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Load (0.4ms)[0m  [1m[34mSELECT "entities".* FROM "entities" /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.7ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 1 AND "reviews"."user_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.0ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 2 AND "reviews"."entity_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.9ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (4, 'Great experience! Highly recommended.', 2, 1, '2025-08-21 18:28:36.109485', '2025-08-21 18:28:36.109485') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 2 AND "reviews"."user_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.8ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 2 AND "reviews"."entity_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.2ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (3, 'Outstanding quality and friendly staff.', 2, 2, '2025-08-21 18:28:36.123241', '2025-08-21 18:28:36.123241') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.1ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.1ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 3 AND "reviews"."user_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.7ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 1 AND "reviews"."entity_id" = 3 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.2ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (3, 'Great experience! Highly recommended.', 1, 3, '2025-08-21 18:28:36.130641', '2025-08-21 18:28:36.130641') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.1ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 4 AND "reviews"."user_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (1.0ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 2 AND "reviews"."entity_id" = 4 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.6ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (5, 'Very satisfied with the service.', 2, 4, '2025-08-21 18:28:36.144200', '2025-08-21 18:28:36.144200') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 5 AND "reviews"."user_id" = 1 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.9ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 1 AND "reviews"."entity_id" = 5 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.3ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (3, 'Great experience! Highly recommended.', 1, 5, '2025-08-21 18:28:36.152768', '2025-08-21 18:28:36.152768') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.5ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.2ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 5 AND "reviews"."user_id" = 3 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.8ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 3 AND "reviews"."entity_id" = 5 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.3ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (5, 'Great experience! Highly recommended.', 3, 5, '2025-08-21 18:28:36.157440', '2025-08-21 18:28:36.157440') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.1ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."entity_id" = 5 AND "reviews"."user_id" = 2 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN /*application='CloudvisionApp'*/[0m
  [1m[36mReview Exists? (0.7ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" WHERE "reviews"."user_id" = 2 AND "reviews"."entity_id" = 5 LIMIT 1 /*application='CloudvisionApp'*/[0m
  [1m[36mReview Create (0.2ms)[0m  [1m[32mINSERT INTO "reviews" ("rating", "comment", "user_id", "entity_id", "created_at", "updated_at") VALUES (3, 'Great experience! Highly recommended.', 2, 5, '2025-08-21 18:28:36.161943', '2025-08-21 18:28:36.161943') RETURNING "id" /*application='CloudvisionApp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT /*application='CloudvisionApp'*/[0m
  [1m[36mCategory Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "categories" /*application='CloudvisionApp'*/[0m
  [1m[36mUser Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "users" /*application='CloudvisionApp'*/[0m
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" /*application='CloudvisionApp'*/[0m
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" /*application='CloudvisionApp'*/[0m
Started HEAD "/" for ::1 at 2025-08-22 00:29:01 +0600
  [1m[36mActiveRecord::SchemaMigration Load (2.7ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='CloudvisionApp'*/[0m
Processing by HomeController#index as */*
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (1.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.7ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (1.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.7ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.8ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.6ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (1.1ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.6ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.5ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.5ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.4ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.7ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.5ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.4ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.6ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.3ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.4ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (0.5ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (2, 3, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.4ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 304.9ms | GC: 0.0ms)
  Rendered layout layouts/application.html.erb (Duration: 324.0ms | GC: 0.0ms)
Completed 200 OK in 475ms (Views: 306.9ms | ActiveRecord: 29.3ms (47 queries, 20 cached) | GC: 1.6ms)


Started GET "/" for ::1 at 2025-08-22 00:29:13 +0600
Processing by HomeController#index as HTML
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (0.6ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.7ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.4ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.3ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.3ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.1ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.3ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (0.3ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (2, 3, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.3ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 66.9ms | GC: 0.0ms)
  Rendered layout layouts/application.html.erb (Duration: 72.0ms | GC: 0.0ms)
Completed 200 OK in 105ms (Views: 66.3ms | ActiveRecord: 6.5ms (47 queries, 20 cached) | GC: 0.0ms)


Started GET "/" for ::1 at 2025-08-22 00:29:14 +0600
Processing by HomeController#index as HTML
  Rendering layout layouts/application.html.erb
  Rendering home/index.html.erb within layouts/application
  [1m[36mCategory Load (0.3ms)[0m  [1m[34mSELECT "categories".* FROM "categories" ORDER BY "categories"."name" ASC /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."category_id" IN (8, 7, 3, 6, 4, 1, 5, 2) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 8 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 7 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "entities" WHERE "entities"."category_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (1.5ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."active" = TRUE ORDER BY "entities"."created_at" DESC LIMIT 6 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mCategory Load (0.2ms)[0m  [1m[34mSELECT "categories".* FROM "categories" WHERE "categories"."id" IN (6, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (1.0ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" WHERE "reviews"."entity_id" IN (5, 4, 3, 2, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Average (1.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 5[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.3ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.2ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 4[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 4 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 3[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 3 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 2[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.1ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 2 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Average (0.1ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mCACHE Review Average (0.0ms)[0m  [1m[34mSELECT AVG("reviews"."rating") FROM "reviews" WHERE "reviews"."entity_id" = 1[0m
  ↳ app/models/entity.rb:34:in 'Entity#average_rating'
  [1m[36mReview Count (0.2ms)[0m  [1m[34mSELECT COUNT(*) FROM "reviews" WHERE "reviews"."entity_id" = 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/models/entity.rb:38:in 'Entity#total_reviews'
  [1m[36mReview Exists? (0.1ms)[0m  [1m[34mSELECT 1 AS one FROM "reviews" LIMIT 1 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mReview Load (0.2ms)[0m  [1m[34mSELECT "reviews".* FROM "reviews" ORDER BY "reviews"."created_at" DESC LIMIT 5 /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mUser Load (0.2ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."id" IN (2, 3, 1) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  [1m[36mEntity Load (0.2ms)[0m  [1m[34mSELECT "entities".* FROM "entities" WHERE "entities"."id" IN (5, 4, 3) /*action='index',application='CloudvisionApp',controller='home'*/[0m
  ↳ app/views/home/<USER>
  Rendered home/index.html.erb within layouts/application (Duration: 66.8ms | GC: 27.0ms)
  Rendered layout layouts/application.html.erb (Duration: 73.2ms | GC: 27.0ms)
Completed 200 OK in 109ms (Views: 66.2ms | ActiveRecord: 7.3ms (47 queries, 20 cached) | GC: 27.0ms)


