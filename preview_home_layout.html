<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudVision - Home Page Layout Preview</title>
    <style>
        /* CSS Variables */
        :root {
          --primary: #0f172a;
          --primary-foreground: #f8fafc;
          --secondary: #f1f5f9;
          --secondary-foreground: #0f172a;
          --accent: #3b82f6;
          --accent-foreground: #ffffff;
          --muted: #f8fafc;
          --muted-foreground: #64748b;
          --border: #e2e8f0;
          --input: #ffffff;
          --ring: #3b82f6;
          --background: #ffffff;
          --foreground: #0f172a;
          --card: #ffffff;
          --card-foreground: #0f172a;
          --popover: #ffffff;
          --popover-foreground: #0f172a;
          --destructive: #ef4444;
          --destructive-foreground: #ffffff;
          --radius: 0.75rem;
        }

        * {
          border-color: var(--border);
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: var(--background);
          color: var(--foreground);
        }

        /* Hero Section */
        .hero {
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
          padding: 120px 0 80px 0;
          position: relative;
          overflow: hidden;
        }

        .hero-content {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 24px;
          text-align: center;
          position: relative;
          z-index: 1;
        }

        .hero-title {
          font-size: clamp(32px, 5vw, 64px);
          font-weight: 800;
          color: var(--primary);
          line-height: 1.1;
          letter-spacing: -0.02em;
          margin-bottom: 24px;
        }

        .hero-subtitle {
          font-size: 20px;
          color: var(--muted-foreground);
          max-width: 600px;
          margin: 0 auto 40px auto;
          line-height: 1.6;
        }

        .hero-actions {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 32px;
          width: 100%;
        }

        /* Search Container */
        .search-container {
          width: 100%;
          max-width: 600px;
          margin: 0 auto;
        }

        .search-wrapper {
          position: relative;
          display: flex;
          align-items: center;
          background: var(--card);
          border: 2px solid var(--border);
          border-radius: 16px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          width: 100%;
          min-height: 64px;
        }

        .search-wrapper:focus-within {
          border-color: var(--accent);
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .search-input {
          flex: 1;
          padding: 20px 120px 20px 56px;
          font-size: 18px;
          border: none;
          border-radius: 16px;
          background: transparent;
          color: var(--foreground);
          outline: none;
        }

        .search-input::placeholder {
          color: var(--muted-foreground);
        }

        .search-icon {
          position: absolute;
          left: 20px;
          top: 50%;
          transform: translateY(-50%);
          color: var(--muted-foreground);
          width: 24px;
          height: 24px;
          pointer-events: none;
        }

        .search-button {
          position: absolute;
          right: 8px;
          top: 50%;
          transform: translateY(-50%);
          background: var(--accent);
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 12px;
          font-weight: 600;
          font-size: 16px;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .search-button:hover {
          background: #2563eb;
          transform: translateY(-50%) scale(1.02);
        }

        /* Hero Buttons */
        .hero-buttons {
          display: flex;
          justify-content: center;
          gap: 20px;
          flex-wrap: wrap;
          width: 100%;
        }

        .hero-btn {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          min-width: 180px;
          padding: 16px 32px;
          text-decoration: none;
          border-radius: 12px;
          font-weight: 600;
          font-size: 16px;
          border: 2px solid transparent;
          white-space: nowrap;
        }

        .hero-btn.btn-primary {
          background: var(--accent);
          color: var(--accent-foreground);
          border-color: var(--accent);
        }

        .hero-btn.btn-secondary {
          background: transparent;
          color: var(--accent);
          border-color: var(--accent);
        }

        .hero-btn:hover {
          transform: translateY(-3px);
          box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
        }

        .hero-btn.btn-primary:hover {
          background: #2563eb;
          border-color: #2563eb;
        }

        .hero-btn.btn-secondary:hover {
          background: var(--accent);
          color: var(--accent-foreground);
        }

        .btn-icon {
          width: 20px;
          height: 20px;
          margin-right: 10px;
        }

        /* Responsive */
        @media (max-width: 768px) {
          .hero-actions {
            gap: 24px;
            padding: 0 16px;
          }
          .hero-buttons {
            gap: 16px;
            flex-direction: column;
            align-items: center;
          }
          .hero-btn {
            min-width: 200px;
            padding: 14px 24px;
          }
          .search-input {
            padding: 18px 100px 18px 48px;
            font-size: 16px;
          }
          .search-button {
            padding: 10px 20px;
            font-size: 14px;
          }
          .search-container {
            max-width: 100%;
            padding: 0 8px;
          }
        }

        @media (max-width: 480px) {
          .hero-actions {
            gap: 20px;
          }
          .hero-btn {
            min-width: 100%;
            max-width: 280px;
          }
          .search-input {
            padding: 16px 90px 16px 44px;
            font-size: 15px;
          }
          .search-button {
            padding: 8px 16px;
            font-size: 13px;
          }
        }
    </style>
</head>
<body>
    <!-- Enhanced Hero Section -->
    <section class="hero">
      <div class="hero-content">
        <h1 class="hero-title">
          Discover exceptional places.<br>
          <span style="color: var(--muted-foreground);">Share your experiences.</span>
        </h1>
        <p class="hero-subtitle">
          Join our community of explorers and discover the best restaurants, cafes, shops, and entertainment venues.
          Rate, review, and share your favorite places with others.
        </p>
        <!-- Enhanced Search with Auto-submit -->
        <div class="hero-actions">
          <form class="search-container">
            <div class="search-wrapper">
              <svg class="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
              <input type="text" class="search-input" placeholder="Search for restaurants, cafes, shops, gyms...">
              <button type="submit" class="search-button">Search</button>
            </div>
          </form>
          <!-- Perfectly Aligned Action Buttons -->
          <div class="hero-buttons">
            <a href="#" class="hero-btn btn-primary">
              <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
              </svg>
              Add a Place
            </a>
            <a href="#" class="hero-btn btn-secondary">
              <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
              Explore Places
            </a>
          </div>
        </div>
      </div>
    </section>
</body>
</html>
