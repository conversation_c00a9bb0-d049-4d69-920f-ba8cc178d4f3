class GooglePlacesService
  include HTTParty
  base_uri "https://maps.googleapis.com/maps/api"

  def initialize
    @api_key = Rails.application.credentials.google_maps_api_key || ENV["GOOGLE_MAPS_API_KEY"]
  end

  def search_nearby_places(latitude, longitude, radius = 5000, type = nil)
    return [] unless @api_key

    options = {
      query: {
        location: "#{latitude},#{longitude}",
        radius: radius,
        key: @api_key
      }
    }

    options[:query][:type] = type if type.present?

    response = self.class.get("/place/nearbysearch/json", options)

    if response.success?
      parse_places_response(response["results"])
    else
      Rails.logger.error "Google Places API Error: #{response.body}"
      []
    end
  end

  def search_places_by_text(query, location = nil)
    # If no API key, return mock data for demo purposes
    unless @api_key
      return generate_mock_places(query, location)
    end

    options = {
      query: {
        query: query,
        key: @api_key
      }
    }

    options[:query][:location] = location if location.present?

    response = self.class.get("/place/textsearch/json", options)

    if response.success?
      parse_places_response(response["results"])
    else
      Rails.logger.error "Google Places API Error: #{response.body}"
      generate_mock_places(query, location) # Fallback to mock data
    end
  end

  def get_place_details(place_id)
    return nil unless @api_key

    options = {
      query: {
        place_id: place_id,
        fields: "name,formatted_address,formatted_phone_number,website,rating,reviews,photos,geometry,types",
        key: @api_key
      }
    }

    response = self.class.get("/place/details/json", options)

    if response.success?
      parse_place_details(response["result"])
    else
      Rails.logger.error "Google Places API Error: #{response.body}"
      nil
    end
  end

  private

  def generate_mock_places(query, location)
    # Generate realistic mock data based on query
    base_places = {
      "restaurant" => [
        { name: "The Gourmet Bistro", types: [ "restaurant", "food" ], rating: 4.5 },
        { name: "Mama's Italian Kitchen", types: [ "restaurant", "meal_takeaway" ], rating: 4.2 },
        { name: "Sushi Zen", types: [ "restaurant", "japanese_restaurant" ], rating: 4.7 },
        { name: "Corner Cafe", types: [ "cafe", "restaurant" ], rating: 4.0 }
      ],
      "coffee" => [
        { name: "Brew & Bean Coffee House", types: [ "cafe", "coffee_shop" ], rating: 4.3 },
        { name: "Artisan Coffee Roasters", types: [ "cafe", "store" ], rating: 4.6 },
        { name: "Morning Glory Cafe", types: [ "cafe", "bakery" ], rating: 4.1 }
      ],
      "gym" => [
        { name: "FitLife Fitness Center", types: [ "gym", "health" ], rating: 4.4 },
        { name: "Iron Paradise Gym", types: [ "gym", "health" ], rating: 4.2 },
        { name: "Yoga Harmony Studio", types: [ "gym", "spa" ], rating: 4.8 }
      ],
      "shop" => [
        { name: "Downtown Electronics", types: [ "electronics_store", "store" ], rating: 4.1 },
        { name: "Fashion Forward Boutique", types: [ "clothing_store", "store" ], rating: 4.3 },
        { name: "Books & More", types: [ "book_store", "store" ], rating: 4.5 }
      ]
    }

    # Find matching places based on query
    query_lower = query.to_s.downcase
    matching_places = []

    base_places.each do |category, places|
      if query_lower.include?(category) || places.any? { |p| p[:name].downcase.include?(query_lower) }
        matching_places.concat(places)
      end
    end

    # If no specific matches, return a mix
    if matching_places.empty?
      matching_places = base_places.values.flatten.sample(6)
    end

    # Generate mock data with realistic coordinates
    base_lat = location&.include?("New York") ? 40.7128 : 37.7749
    base_lng = location&.include?("New York") ? -74.0060 : -122.4194

    matching_places.first(8).map.with_index do |place, index|
      {
        google_place_id: "mock_place_#{index}_#{Time.current.to_i}",
        name: place[:name],
        address: "#{100 + index * 10} Main Street, #{location || 'Downtown'}",
        latitude: base_lat + (rand(-0.01..0.01)),
        longitude: base_lng + (rand(-0.01..0.01)),
        rating: place[:rating],
        types: place[:types],
        price_level: rand(1..4),
        photo_reference: nil
      }
    end
  end

  def parse_places_response(results)
    results.map do |place|
      {
        google_place_id: place["place_id"],
        name: place["name"],
        address: place["vicinity"] || place["formatted_address"],
        latitude: place["geometry"]["location"]["lat"],
        longitude: place["geometry"]["location"]["lng"],
        rating: place["rating"],
        types: place["types"],
        price_level: place["price_level"],
        photo_reference: place["photos"]&.first&.dig("photo_reference")
      }
    end
  end

  def parse_place_details(place)
    return nil unless place

    {
      google_place_id: place["place_id"],
      name: place["name"],
      address: place["formatted_address"],
      phone: place["formatted_phone_number"],
      website: place["website"],
      latitude: place["geometry"]["location"]["lat"],
      longitude: place["geometry"]["location"]["lng"],
      rating: place["rating"],
      types: place["types"],
      reviews: place["reviews"]&.map { |r|
        {
          author: r["author_name"],
          rating: r["rating"],
          text: r["text"],
          time: Time.at(r["time"])
        }
      }
    }
  end
end
