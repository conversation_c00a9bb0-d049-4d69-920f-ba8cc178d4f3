<!-- Professional Hero Section -->
<section class="hero">
  <div class="hero-content">
    <h1 class="hero-title fade-in">
      Discover exceptional places.<br>
      <span style="color: var(--muted-foreground);">Share your experiences.</span>
    </h1>
    <p class="hero-subtitle">
      Join our community of explorers and discover the best restaurants, cafes, shops, and entertainment venues.
      Rate, review, and share your favorite places with others.
    </p>
    <!-- Modern Search -->
    <div class="hero-actions">
      <%= form_with url: search_path, method: :get, local: true, class: "search-container" do |form| %>
        <div style="position: relative;">
          <svg class="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
          </svg>
          <%= form.text_field :q, placeholder: "Search for restaurants, cafes, shops...",
              class: "search-input" %>
        </div>
      <% end %>
      <div style="display: flex; justify-content: center; gap: 16px; margin-top: 24px; flex-wrap: wrap;">
        <% if user_signed_in? %>
          <%= link_to "Add a Place", google_places_search_path, class: "btn btn-primary btn-lg" %>
          <%= link_to "Browse Categories", categories_path, class: "btn btn-secondary btn-lg" %>
        <% else %>
          <%= link_to "Get Started", sign_up_path, class: "btn btn-primary btn-lg" %>
          <%= link_to "Explore Places", entities_path, class: "btn btn-secondary btn-lg" %>
        <% end %>
      </div>
    </div>
  </div>
</section>
<!-- Professional Categories Section -->
<section class="section" style="background: var(--muted);">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">Explore by Category</h2>
      <p class="section-subtitle">
        Discover amazing places across different categories, from cozy cafes to fine dining restaurants.
      </p>
    </div>
    <div class="grid grid-4">
      <% @categories.each do |category| %>
        <%= link_to category_path(category), style: "text-decoration: none; color: inherit;" do %>
          <div class="card" style="text-align: center;">
            <div class="card-content" style="padding: 32px 24px;">
              <div style="font-size: 48px; margin-bottom: 16px; transition: transform 0.2s ease;">
                <%= case category.name
                    when "Restaurants" then "🍽️"
                    when "Shops" then "🛍️"
                    when "Entertainment" then "🎬"
                    when "Hotels" then "🏨"
                    when "Services" then "🔧"
                    when "Health & Fitness" then "💪"
                    when "Education" then "📚"
                    when "Automotive" then "🚗"
                    else "📍"
                    end %>
              </div>
              <h3 style="font-size: 18px; font-weight: 600; color: var(--foreground); margin-bottom: 8px;"><%= category.name %></h3>
              <p style="color: var(--muted-foreground); font-size: 14px;">
                <%= pluralize(category.entities.count, 'place') %>
              </p>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
  </div>
</section>
<!-- Professional Featured Places Section -->
<section class="section">
  <div class="container">
    <div class="flex justify-between items-end mb-12">
      <div>
        <h2 class="section-title text-left mb-4">Featured Places</h2>
        <p class="text-slate-600">Discover the most popular and highly-rated places in our community</p>
      </div>
      <%= link_to "View All Places", entities_path, class: "btn btn-secondary" %>
    </div>
    <div class="grid-3">
      <% @featured_entities.each do |entity| %>
        <%= link_to entity_path(entity), class: "group" do %>
          <div class="card">
            <div class="card-header">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center text-2xl">
                    <%= case entity.category.name
                        when "Restaurants" then "🍽️"
                        when "Shops" then "🛍️"
                        when "Entertainment" then "🎬"
                        when "Hotels" then "🏨"
                        when "Services" then "🔧"
                        when "Health & Fitness" then "💪"
                        when "Education" then "📚"
                        when "Automotive" then "🚗"
                        else "📍"
                        end %>
                  </div>
                  <div>
                    <h3 class="font-semibold text-slate-900 group-hover:text-slate-700 transition-colors">
                      <%= entity.name %>
                    </h3>
                    <p class="text-sm text-slate-500"><%= entity.category.name %></p>
                  </div>
                </div>
                <div class="flex items-center space-x-1">
                  <div class="rating-stars">
                    <% 5.times do |i| %>
                      <svg class="rating-star <%= i < entity.average_rating ? 'active' : '' %>" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                      </svg>
                    <% end %>
                  </div>
                  <span class="text-sm text-slate-500 ml-1">(<%= entity.total_reviews %>)</span>
                </div>
              </div>
            </div>
            <div class="card-content">
              <p class="text-slate-600 text-sm leading-relaxed">
                <%= truncate(entity.description, length: 120) %>
              </p>
            </div>
            <% if entity.address.present? %>
              <div class="card-footer">
                <div class="flex items-center text-slate-500 text-sm">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                  </svg>
                  <%= truncate(entity.address, length: 40) %>
                </div>
              </div>
            <% end %>
          </div>
        <% end %>
      <% end %>
    </div>
  </div>
</section>
<!-- Recent Reviews -->
<% if @recent_reviews.any? %>
  <div>
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Recent Reviews</h2>
    <div class="space-y-4">
      <% @recent_reviews.each do |review| %>
        <div class="bg-white rounded-lg shadow-md p-4">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center mb-2">
                <div class="flex text-yellow-400 mr-2">
                  <% 5.times do |i| %>
                    <svg class="w-4 h-4 <%= i < review.rating ? 'text-yellow-400' : 'text-gray-300' %>" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                  <% end %>
                </div>
                <span class="font-semibold text-gray-800"><%= review.user.full_name %></span>
                <span class="text-gray-500 text-sm ml-2">reviewed</span>
                <%= link_to review.entity.name, entity_path(review.entity), class: "text-blue-600 hover:text-blue-800 font-semibold ml-1" %>
              </div>
              <% if review.comment.present? %>
                <p class="text-gray-700"><%= truncate(review.comment, length: 150) %></p>
              <% end %>
            </div>
            <span class="text-sm text-gray-500"><%= time_ago_in_words(review.created_at) %> ago</span>
          </div>
        </div>
      <% end %>
    </div>
  </div>
<% end %>
