<!-- Enhanced Hero Section -->
<section class="hero">
  <div class="hero-content">
    <h1 class="hero-title fade-in">
      Discover exceptional places.<br>
      <span style="color: var(--muted-foreground);">Share your experiences.</span>
    </h1>
    <p class="hero-subtitle">
      Join our community of <%= pluralize(User.count, 'explorer') %> and discover the best restaurants, cafes, shops, and entertainment venues.
      Rate, review, and share your favorite places with others.
    </p>
    <!-- Enhanced Search with Auto-submit -->
    <div class="hero-actions">
      <%= form_with url: search_path, method: :get, local: true, class: "search-container" do |form| %>
        <div style="position: relative;">
          <svg class="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
          </svg>
          <%= form.text_field :q,
              placeholder: "Search for restaurants, cafes, shops, gyms...",
              class: "search-input",
              onkeypress: "if(event.key==='Enter'){this.form.submit();}" %>
          <%= form.submit "Search", class: "search-button" %>
        </div>
      <% end %>
      <!-- Enhanced Action Buttons -->
      <div style="display: flex; justify-content: center; gap: 16px; margin-top: 32px; flex-wrap: wrap;">
        <% if user_signed_in? %>
          <%= link_to google_places_search_path, class: "btn btn-primary btn-lg hero-btn" do %>
            <svg style="width: 20px; height: 20px; margin-right: 8px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            Add a Place
          <% end %>
          <%= link_to categories_path, class: "btn btn-secondary btn-lg hero-btn" do %>
            <svg style="width: 20px; height: 20px; margin-right: 8px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
            </svg>
            Browse Categories
          <% end %>
        <% else %>
          <%= link_to sign_up_path, class: "btn btn-primary btn-lg hero-btn" do %>
            <svg style="width: 20px; height: 20px; margin-right: 8px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"/>
            </svg>
            Get Started
          <% end %>
          <%= link_to entities_path, class: "btn btn-secondary btn-lg hero-btn" do %>
            <svg style="width: 20px; height: 20px; margin-right: 8px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
            Explore Places
          <% end %>
        <% end %>
      </div>
    </div>
  </div>
</section>
<!-- Stats Section -->
<section class="section" style="background: var(--card); border-top: 1px solid var(--border); border-bottom: 1px solid var(--border);">
  <div class="container">
    <div class="grid grid-4" style="text-align: center;">
      <div>
        <div style="font-size: 32px; font-weight: 700; color: var(--accent); margin-bottom: 8px;">
          <%= Entity.count %>
        </div>
        <div style="color: var(--muted-foreground); font-weight: 500;">Places Listed</div>
      </div>
      <div>
        <div style="font-size: 32px; font-weight: 700; color: var(--accent); margin-bottom: 8px;">
          <%= Review.count %>
        </div>
        <div style="color: var(--muted-foreground); font-weight: 500;">Reviews Written</div>
      </div>
      <div>
        <div style="font-size: 32px; font-weight: 700; color: var(--accent); margin-bottom: 8px;">
          <%= User.count %>
        </div>
        <div style="color: var(--muted-foreground); font-weight: 500;">Community Members</div>
      </div>
      <div>
        <div style="font-size: 32px; font-weight: 700; color: var(--accent); margin-bottom: 8px;">
          <%= Category.count %>
        </div>
        <div style="color: var(--muted-foreground); font-weight: 500;">Categories</div>
      </div>
    </div>
  </div>
</section>
<!-- How It Works Section -->
<section class="section">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">How It Works</h2>
      <p class="section-subtitle">
        Join our community and start discovering amazing places in just three simple steps.
      </p>
    </div>
    <div class="grid grid-3" style="gap: 32px;">
      <div style="text-align: center;">
        <div style="width: 80px; height: 80px; background: var(--accent); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px; color: white; font-size: 32px;">
          🔍
        </div>
        <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 16px; color: var(--foreground);">Discover</h3>
        <p style="color: var(--muted-foreground); line-height: 1.6;">
          Search and explore amazing places in your area. From restaurants to entertainment venues, find exactly what you're looking for.
        </p>
      </div>
      <div style="text-align: center;">
        <div style="width: 80px; height: 80px; background: var(--accent); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px; color: white; font-size: 32px;">
          ⭐
        </div>
        <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 16px; color: var(--foreground);">Review</h3>
        <p style="color: var(--muted-foreground); line-height: 1.6;">
          Share your experiences by rating and reviewing places you've visited. Help others make informed decisions.
        </p>
      </div>
      <div style="text-align: center;">
        <div style="width: 80px; height: 80px; background: var(--accent); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px; color: white; font-size: 32px;">
          🤝
        </div>
        <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 16px; color: var(--foreground);">Connect</h3>
        <p style="color: var(--muted-foreground); line-height: 1.6;">
          Join a community of explorers, share recommendations, and discover hidden gems through trusted reviews.
        </p>
      </div>
    </div>
  </div>
</section>
<!-- Enhanced Categories Section -->
<section class="section" style="background: var(--muted);">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">Explore by Category</h2>
      <p class="section-subtitle">
        Discover amazing places across <%= pluralize(@categories.count, 'category') %>, from cozy cafes to fine dining restaurants, entertainment venues to essential services.
      </p>
    </div>
    <div class="grid grid-4">
      <% @categories.each do |category| %>
        <%= link_to category_path(category), style: "text-decoration: none; color: inherit;" do %>
          <div class="card" style="text-align: center;">
            <div class="card-content" style="padding: 32px 24px;">
              <div style="font-size: 48px; margin-bottom: 16px; transition: transform 0.2s ease;">
                <%= case category.name
                    when "Restaurants" then "🍽️"
                    when "Shops" then "🛍️"
                    when "Entertainment" then "🎬"
                    when "Hotels" then "🏨"
                    when "Services" then "🔧"
                    when "Health & Fitness" then "💪"
                    when "Education" then "📚"
                    when "Automotive" then "🚗"
                    else "📍"
                    end %>
              </div>
              <h3 style="font-size: 18px; font-weight: 600; color: var(--foreground); margin-bottom: 8px;"><%= category.name %></h3>
              <p style="color: var(--muted-foreground); font-size: 14px;">
                <%= pluralize(category.entities.count, 'place') %>
              </p>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
  </div>
</section>
<!-- Professional Featured Places Section -->
<section class="section">
  <div class="section-content">
    <div style="display: flex; justify-content: space-between; align-items: flex-end; margin-bottom: 48px;">
      <div>
        <h2 class="section-title" style="text-align: left; margin-bottom: 16px;">Featured Places</h2>
        <p style="color: var(--muted-foreground);">Discover the most popular and highly-rated places in our community</p>
      </div>
      <%= link_to "View All Places", entities_path, class: "btn btn-secondary" %>
    </div>
    <div class="grid grid-3">
      <% @featured_entities.each do |entity| %>
        <%= link_to entity_path(entity), class: "group" do %>
          <div class="card">
            <div class="card-header">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center text-2xl">
                    <%= case entity.category.name
                        when "Restaurants" then "🍽️"
                        when "Shops" then "🛍️"
                        when "Entertainment" then "🎬"
                        when "Hotels" then "🏨"
                        when "Services" then "🔧"
                        when "Health & Fitness" then "💪"
                        when "Education" then "📚"
                        when "Automotive" then "🚗"
                        else "📍"
                        end %>
                  </div>
                  <div>
                    <h3 class="font-semibold text-slate-900 group-hover:text-slate-700 transition-colors">
                      <%= entity.name %>
                    </h3>
                    <p class="text-sm text-slate-500"><%= entity.category.name %></p>
                  </div>
                </div>
                <div class="flex items-center space-x-1">
                  <div class="rating-stars">
                    <% 5.times do |i| %>
                      <svg class="rating-star <%= i < entity.average_rating ? 'active' : '' %>" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                      </svg>
                    <% end %>
                  </div>
                  <span class="text-sm text-slate-500 ml-1">(<%= entity.total_reviews %>)</span>
                </div>
              </div>
            </div>
            <div class="card-content">
              <p class="text-slate-600 text-sm leading-relaxed">
                <%= truncate(entity.description, length: 120) %>
              </p>
            </div>
            <% if entity.address.present? %>
              <div class="card-footer">
                <div class="flex items-center text-slate-500 text-sm">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                  </svg>
                  <%= truncate(entity.address, length: 40) %>
                </div>
              </div>
            <% end %>
          </div>
        <% end %>
      <% end %>
    </div>
  </div>
</section>
<!-- Call to Action Section -->
<section class="section" style="background: linear-gradient(135deg, var(--accent) 0%, var(--accent-foreground) 100%); color: white;">
  <div class="container" style="text-align: center;">
    <h2 style="font-size: 36px; font-weight: 700; margin-bottom: 16px; color: white;">
      Ready to Explore?
    </h2>
    <p style="font-size: 18px; margin-bottom: 32px; opacity: 0.9; max-width: 600px; margin-left: auto; margin-right: auto;">
      <% if user_signed_in? %>
        Start adding your favorite places to help others discover amazing experiences in your area.
      <% else %>
        Join our community of explorers and start discovering the best places around you.
      <% end %>
    </p>
    <div style="display: flex; justify-content: center; gap: 16px; flex-wrap: wrap;">
      <% if user_signed_in? %>
        <%= link_to google_places_search_path, class: "btn", style: "background: white; color: var(--accent); border: none; padding: 16px 32px; font-weight: 600; font-size: 16px; border-radius: 8px; text-decoration: none; display: inline-flex; align-items: center; transition: all 0.2s ease;" do %>
          <svg style="width: 20px; height: 20px; margin-right: 8px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          Add Your First Place
        <% end %>
        <%= link_to entities_path, class: "btn", style: "background: transparent; color: white; border: 2px solid white; padding: 14px 32px; font-weight: 600; font-size: 16px; border-radius: 8px; text-decoration: none; display: inline-flex; align-items: center; transition: all 0.2s ease;" do %>
          <svg style="width: 20px; height: 20px; margin-right: 8px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
          </svg>
          Explore All Places
        <% end %>
      <% else %>
        <%= link_to sign_up_path, class: "btn", style: "background: white; color: var(--accent); border: none; padding: 16px 32px; font-weight: 600; font-size: 16px; border-radius: 8px; text-decoration: none; display: inline-flex; align-items: center; transition: all 0.2s ease;" do %>
          <svg style="width: 20px; height: 20px; margin-right: 8px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
          </svg>
          Join Our Community
        <% end %>
        <%= link_to entities_path, class: "btn", style: "background: transparent; color: white; border: 2px solid white; padding: 14px 32px; font-weight: 600; font-size: 16px; border-radius: 8px; text-decoration: none; display: inline-flex; align-items: center; transition: all 0.2s ease;" do %>
          <svg style="width: 20px; height: 20px; margin-right: 8px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
          </svg>
          Browse Places
        <% end %>
      <% end %>
    </div>
  </div>
</section>
<!-- Recent Reviews Section -->
<% if @recent_reviews.any? %>
  <section class="section" style="background: var(--background);">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Recent Reviews</h2>
        <p class="section-subtitle">See what our community is saying about their experiences</p>
      </div>
      <div class="grid grid-1" style="max-width: 800px; margin: 0 auto;">
        <% @recent_reviews.each do |review| %>
          <div class="card">
            <div class="card-content" style="padding: 20px;">
              <div style="display: flex; align-items: flex-start; gap: 16px;">
                <!-- User Avatar -->
                <div style="width: 40px; height: 40px; background: var(--accent); border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                  <span style="color: white; font-weight: 600; font-size: 16px;">
                    <%= review.user.full_name.first.upcase %>
                  </span>
                </div>
                <!-- Review Content -->
                <div style="flex: 1; min-width: 0;">
                  <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px; flex-wrap: wrap;">
                    <span style="font-weight: 600; color: var(--foreground); font-size: 15px;"><%= review.user.full_name %></span>
                    <span style="color: var(--muted-foreground); font-size: 14px;">rated</span>
                    <%= link_to review.entity.name, entity_path(review.entity),
                        style: "color: var(--accent); font-weight: 600; text-decoration: none; font-size: 15px;" %>
                    <div class="rating-stars" style="margin-left: 4px;">
                      <% 5.times do |i| %>
                        <svg class="rating-star <%= i < review.rating ? 'active' : '' %>" fill="currentColor" viewBox="0 0 20 20" style="width: 16px; height: 16px;">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                      <% end %>
                    </div>
                  </div>
                  <% if review.comment.present? %>
                    <p style="color: var(--muted-foreground); line-height: 1.6; margin-bottom: 8px; font-size: 14px;">
                      "<%= truncate(review.comment, length: 120) %>"
                    </p>
                  <% end %>
                  <span style="font-size: 13px; color: var(--muted-foreground);">
                    <%= time_ago_in_words(review.created_at) %> ago
                  </span>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </section>
<% end %>
