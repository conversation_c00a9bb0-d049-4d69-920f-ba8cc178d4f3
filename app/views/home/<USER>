<!-- Hero Section -->
<div class="hero-gradient text-white rounded-lg p-12 mb-12 relative overflow-hidden">
  <div class="absolute inset-0 bg-black opacity-10"></div>
  <div class="relative z-10 text-center">
    <h1 class="text-5xl font-bold mb-6 float-animation">Discover & Rate Amazing Places</h1>
    <p class="text-xl mb-8 opacity-90">Find the best restaurants, shops, entertainment venues, and more in your area</p>
    <!-- Search Form -->
    <%= form_with url: search_path, method: :get, local: true, class: "max-w-lg mx-auto" do |form| %>
      <div class="flex glass rounded-lg p-2">
        <%= form.text_field :q, placeholder: "Search for places...",
            class: "flex-1 px-6 py-4 bg-transparent text-white placeholder-white placeholder-opacity-70 focus:outline-none text-lg" %>
        <%= form.submit "Search",
            class: "btn bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-all" %>
      </div>
    <% end %>
  </div>
</div>
<!-- Categories Section -->
<div class="mb-12">
  <h2 class="text-2xl font-bold text-gray-800 mb-6">Browse by Category</h2>
  <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
    <% @categories.each do |category| %>
      <%= link_to category_path(category), class: "bg-white rounded-lg shadow-md p-6 text-center hover:shadow-lg transition-shadow" do %>
        <div class="text-3xl mb-2">
          <%= case category.name
              when "Restaurants" then "🍽️"
              when "Shops" then "🛍️"
              when "Entertainment" then "🎬"
              when "Hotels" then "🏨"
              when "Services" then "🔧"
              when "Health & Fitness" then "💪"
              when "Education" then "📚"
              when "Automotive" then "🚗"
              else "📍"
              end %>
        </div>
        <h3 class="font-semibold text-gray-800"><%= category.name %></h3>
        <p class="text-sm text-gray-600 mt-1"><%= pluralize(category.entities.count, 'place') %></p>
      <% end %>
    <% end %>
  </div>
</div>
<!-- Featured Entities -->
<div class="mb-12">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-2xl font-bold text-gray-800">Featured Places</h2>
    <%= link_to "View All", entities_path, class: "text-blue-600 hover:text-blue-800 font-semibold" %>
  </div>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
    <% @featured_entities.each do |entity| %>
      <div class="card card-hover overflow-hidden">
        <div class="h-48 bg-gray-200 flex items-center justify-center">
          <span class="text-4xl">
            <%= case entity.category.name
                when "Restaurants" then "🍽️"
                when "Shops" then "🛍️"
                when "Entertainment" then "🎬"
                when "Hotels" then "🏨"
                when "Services" then "🔧"
                when "Health & Fitness" then "💪"
                when "Education" then "📚"
                when "Automotive" then "🚗"
                else "📍"
                end %>
          </span>
        </div>
        <div class="p-4">
          <h3 class="font-semibold text-lg mb-2">
            <%= link_to entity.name, entity_path(entity), class: "text-gray-800 hover:text-blue-600" %>
          </h3>
          <p class="text-gray-600 text-sm mb-2"><%= truncate(entity.description, length: 100) %></p>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-500"><%= entity.category.name %></span>
            <div class="flex items-center">
              <div class="flex text-yellow-400">
                <% 5.times do |i| %>
                  <svg class="w-4 h-4 <%= i < entity.average_rating ? 'text-yellow-400' : 'text-gray-300' %>" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                <% end %>
              </div>
              <span class="ml-1 text-sm text-gray-600">(<%= entity.total_reviews %>)</span>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
<!-- Recent Reviews -->
<% if @recent_reviews.any? %>
  <div>
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Recent Reviews</h2>
    <div class="space-y-4">
      <% @recent_reviews.each do |review| %>
        <div class="bg-white rounded-lg shadow-md p-4">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center mb-2">
                <div class="flex text-yellow-400 mr-2">
                  <% 5.times do |i| %>
                    <svg class="w-4 h-4 <%= i < review.rating ? 'text-yellow-400' : 'text-gray-300' %>" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                  <% end %>
                </div>
                <span class="font-semibold text-gray-800"><%= review.user.full_name %></span>
                <span class="text-gray-500 text-sm ml-2">reviewed</span>
                <%= link_to review.entity.name, entity_path(review.entity), class: "text-blue-600 hover:text-blue-800 font-semibold ml-1" %>
              </div>
              <% if review.comment.present? %>
                <p class="text-gray-700"><%= truncate(review.comment, length: 150) %></p>
              <% end %>
            </div>
            <span class="text-sm text-gray-500"><%= time_ago_in_words(review.created_at) %> ago</span>
          </div>
        </div>
      <% end %>
    </div>
  </div>
<% end %>
