<div class="page-container">
  <div class="page-header">
    <h1 class="page-title">Add New Place</h1>
    <p class="page-subtitle">Search for real businesses and places from Google Maps to add to your directory</p>
  </div>
  <!-- Search Form -->
  <div class="card" style="margin-bottom: 32px;">
    <div class="card-content" style="padding: 32px;">
      <%= form_with url: google_places_search_path, method: :get, local: true do |form| %>
        <div class="grid grid-2">
          <div class="form-group">
            <%= form.label :q, "Search for places", class: "form-label" %>
            <%= form.text_field :q,
                value: @query,
                placeholder: "e.g., restaurants, coffee shops, gyms",
                class: "form-input" %>
          </div>
          <div class="form-group">
            <%= form.label :location, "Location", class: "form-label" %>
            <%= form.text_field :location,
                value: params[:location],
                placeholder: "e.g., New York, NY",
                class: "form-input" %>
          </div>
        </div>
        <div style="display: flex; justify-content: center; margin-top: 24px; padding-top: 24px; border-top: 1px solid var(--border);">
          <%= form.submit "Search Google Places", class: "btn btn-primary btn-lg" %>
        </div>
      <% end %>
    </div>
  </div>
  <!-- Results -->
  <% if @places.any? %>
    <div style="margin-bottom: 32px;">
      <h2 style="font-size: 24px; font-weight: 700; color: var(--foreground); margin-bottom: 24px;">
        Found <%= @places.count %> places
      </h2>
      <div class="grid grid-3">
        <% @places.each do |place| %>
          <div class="card">
            <div class="card-content">
              <h3 style="font-size: 18px; font-weight: 600; color: var(--foreground); margin-bottom: 8px;">
                <%= place[:name] %>
              </h3>
              <% if place[:address].present? %>
                <p style="color: var(--muted-foreground); font-size: 14px; margin-bottom: 8px; display: flex; align-items: center;">
                  <svg style="width: 16px; height: 16px; margin-right: 4px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                  </svg>
                  <%= place[:address] %>
                </p>
              <% end %>
              <% if place[:rating].present? %>
                <div style="display: flex; align-items: center; margin-bottom: 12px;">
                  <div class="rating-stars" style="margin-right: 8px;">
                    <% place[:rating].to_i.times do %>
                      <svg class="rating-star active" fill="currentColor" viewBox="0 0 20 20" style="width: 16px; height: 16px;">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                      </svg>
                    <% end %>
                  </div>
                  <span style="font-size: 14px; color: var(--muted-foreground);"><%= place[:rating] %> rating</span>
                </div>
              <% end %>
              <% if place[:types].present? %>
                <div style="margin-bottom: 16px;">
                  <% place[:types].first(3).each do |type| %>
                    <span style="display: inline-block; background: var(--muted); color: var(--muted-foreground); font-size: 12px; padding: 4px 8px; border-radius: 12px; margin-right: 4px; margin-bottom: 4px;">
                      <%= type.humanize %>
                    </span>
                  <% end %>
                </div>
              <% end %>
              <%= form_with url: google_places_import_path, method: :post, local: true do |form| %>
                <%= form.hidden_field :place_data, value: place.to_json %>
                <%= form.submit "Import This Place", class: "btn btn-primary", style: "width: 100%;" %>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  <% elsif @query.present? %>
    <div class="card" style="text-align: center;">
      <div class="card-content" style="padding: 48px 24px;">
        <div style="font-size: 48px; margin-bottom: 16px;">🔍</div>
        <h3 style="font-size: 18px; font-weight: 600; color: var(--foreground); margin-bottom: 8px;">No places found</h3>
        <p style="color: var(--muted-foreground);">Try searching with different keywords or location.</p>
      </div>
    </div>
  <% else %>
    <div class="card" style="text-align: center;">
      <div class="card-content" style="padding: 48px 24px;">
        <div style="font-size: 48px; margin-bottom: 16px;">🗺️</div>
        <h3 style="font-size: 18px; font-weight: 600; color: var(--foreground); margin-bottom: 8px;">Search Google Places</h3>
        <p style="color: var(--muted-foreground);">Enter a search term above to find real businesses and places to import.</p>
      </div>
    </div>
  <% end %>
</div>
