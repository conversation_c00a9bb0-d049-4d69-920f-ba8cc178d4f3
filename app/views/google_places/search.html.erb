<div class="mb-8">
  <h1 class="text-3xl font-bold text-white mb-4">Import Places from Google Maps</h1>
  <p class="text-white opacity-90">Search for real businesses and places to add to your directory</p>
</div>
<!-- Search Form -->
<div class="card p-6 mb-8">
  <%= form_with url: google_places_search_path, method: :get, local: true, class: "space-y-4" do |form| %>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <%= form.label :q, "Search for places", class: "block text-sm font-semibold text-gray-700 mb-2" %>
        <%= form.text_field :q,
            value: @query,
            placeholder: "e.g., restaurants, coffee shops, gyms",
            class: "form-input" %>
      </div>
      <div>
        <%= form.label :location, "Location", class: "block text-sm font-semibold text-gray-700 mb-2" %>
        <%= form.text_field :location,
            value: params[:location],
            placeholder: "e.g., New York, NY",
            class: "form-input" %>
      </div>
    </div>
    <div>
      <%= form.submit "Search Google Places", class: "btn btn-primary" %>
    </div>
  <% end %>
</div>
<!-- Results -->
<% if @places.any? %>
  <div class="mb-8">
    <h2 class="text-2xl font-bold text-white mb-6">Found <%= @places.count %> places</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <% @places.each do |place| %>
        <div class="card p-6">
          <h3 class="font-semibold text-lg mb-2"><%= place[:name] %></h3>
          <% if place[:address].present? %>
            <p class="text-gray-600 text-sm mb-2 flex items-center">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
              </svg>
              <%= place[:address] %>
            </p>
          <% end %>
          <% if place[:rating].present? %>
            <div class="flex items-center mb-3">
              <div class="flex text-yellow-400 mr-2">
                <% place[:rating].to_i.times do %>
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                <% end %>
              </div>
              <span class="text-sm text-gray-600"><%= place[:rating] %> rating</span>
            </div>
          <% end %>
          <% if place[:types].present? %>
            <div class="mb-4">
              <% place[:types].first(3).each do |type| %>
                <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mr-1 mb-1">
                  <%= type.humanize %>
                </span>
              <% end %>
            </div>
          <% end %>
          <%= form_with url: google_places_import_path, method: :post, local: true do |form| %>
            <%= form.hidden_field :place_data, value: place.to_json %>
            <%= form.submit "Import This Place", class: "btn btn-primary w-full" %>
          <% end %>
        </div>
      <% end %>
    </div>
  </div>
<% elsif @query.present? %>
  <div class="card p-8 text-center">
    <div class="text-4xl mb-4">🔍</div>
    <h3 class="text-lg font-semibold text-gray-800 mb-2">No places found</h3>
    <p class="text-gray-600">Try searching with different keywords or location.</p>
  </div>
<% else %>
  <div class="card p-8 text-center">
    <div class="text-4xl mb-4">🗺️</div>
    <h3 class="text-lg font-semibold text-gray-800 mb-2">Search Google Places</h3>
    <p class="text-gray-600">Enter a search term above to find real businesses and places to import.</p>
  </div>
<% end %>
