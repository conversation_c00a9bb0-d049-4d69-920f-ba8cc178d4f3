<div class="mb-8">
  <h1 class="text-3xl font-bold text-gray-800 mb-4">Browse Categories</h1>
  <p class="text-gray-600">Explore places by category to find exactly what you're looking for.</p>
</div>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  <% @categories.each do |category| %>
    <%= link_to category_path(category), class: "bg-white rounded-lg shadow-md p-8 text-center hover:shadow-lg transition-shadow group" do %>
      <div class="text-6xl mb-4">
        <%= case category.name
            when "Restaurants" then "🍽️"
            when "Shops" then "🛍️"
            when "Entertainment" then "🎬"
            when "Hotels" then "🏨"
            when "Services" then "🔧"
            when "Health & Fitness" then "💪"
            when "Education" then "📚"
            when "Automotive" then "🚗"
            else "📍"
            end %>
      </div>

      <h3 class="text-xl font-bold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors">
        <%= category.name %>
      </h3>

      <% if category.description.present? %>
        <p class="text-gray-600 text-sm mb-4"><%= category.description %></p>
      <% end %>

      <div class="flex items-center justify-center space-x-4 text-sm text-gray-500">
        <span><%= pluralize(category.entities.count, 'place') %></span>
      </div>
    <% end %>
  <% end %>
</div>
