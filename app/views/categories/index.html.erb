<div class="page-container">
  <div class="page-header">
    <h1 class="page-title">Browse Categories</h1>
    <p class="page-subtitle">Explore places by category to find exactly what you're looking for.</p>
  </div>
  <div class="grid grid-3">
    <% @categories.each do |category| %>
      <%= link_to category_path(category), style: "text-decoration: none; color: inherit;" do %>
        <div class="card" style="text-align: center; transition: all 0.3s ease;">
          <div class="card-content" style="padding: 40px 24px;">
            <div style="font-size: 64px; margin-bottom: 20px; transition: transform 0.2s ease;">
              <%= case category.name
                  when "Restaurants" then "🍽️"
                  when "Shops" then "🛍️"
                  when "Entertainment" then "🎬"
                  when "Hotels" then "🏨"
                  when "Services" then "🔧"
                  when "Health & Fitness" then "💪"
                  when "Education" then "📚"
                  when "Automotive" then "🚗"
                  else "📍"
                  end %>
            </div>
            <h3 style="font-size: 20px; font-weight: 700; color: var(--foreground); margin-bottom: 8px; transition: color 0.2s ease;">
              <%= category.name %>
            </h3>
            <% if category.description.present? %>
              <p style="color: var(--muted-foreground); font-size: 14px; margin-bottom: 16px; line-height: 1.5;">
                <%= category.description %>
              </p>
            <% end %>
            <div style="display: flex; align-items: center; justify-content: center; font-size: 14px; color: var(--muted-foreground);">
              <span style="background: var(--muted); padding: 4px 12px; border-radius: 12px; font-weight: 500;">
                <%= pluralize(category.entities.count, 'place') %>
              </span>
            </div>
          </div>
        </div>
      <% end %>
    <% end %>
  </div>
</div>
