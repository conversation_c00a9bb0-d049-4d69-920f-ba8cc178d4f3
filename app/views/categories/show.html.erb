<div class="page-container">
  <div class="page-header">
    <div style="display: flex; align-items: center;">
      <%= link_to categories_path, style: "color: var(--accent); margin-right: 16px; text-decoration: none;" do %>
        <svg style="width: 24px; height: 24px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
        </svg>
      <% end %>
      <div>
        <div style="display: flex; align-items: center; margin-bottom: 8px;">
          <span style="font-size: 48px; margin-right: 16px;">
            <%= case @category.name
                when "Restaurants" then "🍽️"
                when "Shops" then "🛍️"
                when "Entertainment" then "🎬"
                when "Hotels" then "🏨"
                when "Services" then "🔧"
                when "Health & Fitness" then "💪"
                when "Education" then "📚"
                when "Automotive" then "🚗"
                else "📍"
                end %>
          </span>
          <h1 class="page-title" style="margin-bottom: 0;"><%= @category.name %></h1>
        </div>
        <% if @category.description.present? %>
          <p class="page-subtitle"><%= @category.description %></p>
        <% end %>
        <p style="font-size: 14px; color: var(--muted-foreground); margin-top: 8px;">
          <%= pluralize(@entities.total_count, 'place') %> in this category
        </p>
      </div>
    </div>
  </div>
  <!-- Entities Grid -->
  <% if @entities.any? %>
    <div class="entities-grid" style="margin-bottom: 32px;">
      <% @entities.each do |entity| %>
        <div class="card">
          <div style="height: 192px; background: var(--muted); display: flex; align-items: center; justify-content: center;">
            <span style="font-size: 48px;">
              <%= case entity.category.name
                when "Restaurants" then "🍽️"
                when "Shops" then "🛍️"
                when "Entertainment" then "🎬"
                when "Hotels" then "🏨"
                when "Services" then "🔧"
                when "Health & Fitness" then "💪"
                when "Education" then "📚"
                when "Automotive" then "🚗"
                else "📍"
                end %>
            </span>
          </div>
          <div class="card-content">
            <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 8px;">
              <%= link_to entity.name, entity_path(entity), style: "color: var(--foreground); text-decoration: none;" %>
            </h3>
            <p style="color: var(--muted-foreground); font-size: 14px; margin-bottom: 12px;"><%= truncate(entity.description, length: 120) %></p>
            <div style="display: flex; align-items: center; margin-bottom: 12px;">
              <div style="display: flex; gap: 4px; margin-right: 12px;">
                <% 5.times do |i| %>
                  <svg style="width: 20px; height: 20px; color: <%= i < entity.average_rating ? '#f59e0b' : '#d1d5db' %>;" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                <% end %>
              </div>
              <span style="font-size: 16px; font-weight: 600; color: var(--foreground); margin-right: 8px;"><%= entity.average_rating %></span>
              <span style="color: var(--muted-foreground);">(<%= pluralize(entity.total_reviews, 'review') %>)</span>
            </div>
            <% if entity.address.present? %>
              <p style="color: var(--muted-foreground); font-size: 14px; display: flex; align-items: center;">
                <svg style="width: 16px; height: 16px; margin-right: 4px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                </svg>
                <%= truncate(entity.address, length: 40) %>
              </p>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    <!-- Pagination -->
    <%= paginate @entities if respond_to?(:paginate) %>
  <% else %>
    <div class="card">
      <div class="card-content" style="text-align: center; padding: 64px 32px;">
        <div style="font-size: 80px; margin-bottom: 24px;">
          <%= case @category.name
            when "Restaurants" then "🍽️"
            when "Shops" then "🛍️"
            when "Entertainment" then "🎬"
            when "Hotels" then "🏨"
            when "Services" then "🔧"
            when "Health & Fitness" then "💪"
            when "Education" then "📚"
            when "Automotive" then "🚗"
            else "📍"
            end %>
        </div>
        <h3 style="font-size: 24px; font-weight: 600; color: var(--foreground); margin-bottom: 12px;">No places in this category yet</h3>
        <p style="color: var(--muted-foreground); margin-bottom: 32px; max-width: 400px; margin-left: auto; margin-right: auto;">Be the first to add a place in the <%= @category.name.downcase %> category and help others discover great spots!</p>
        <% if user_signed_in? %>
          <%= link_to new_entity_path(category_id: @category.id), class: "btn btn-accent btn-lg" do %>
            <svg style="width: 20px; height: 20px; margin-right: 8px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            Add First Place
          <% end %>
        <% else %>
          <div style="display: flex; gap: 12px; justify-content: center; flex-wrap: wrap;">
            <%= link_to "Sign In", sign_in_path, class: "btn btn-accent btn-lg" %>
            <%= link_to "Sign Up", sign_up_path, class: "btn btn-outline btn-lg" %>
          </div>
        <% end %>
      </div>
    </div>
  <% end %>
</div>
