<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "Cloudvision App" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed top-0 left-0 right-0 z-50">
      <div class="container mx-auto px-4">
        <div class="flex justify-between items-center py-4">
          <%= link_to root_path, class: "text-2xl font-bold text-blue-600" do %>
            CloudVision
          <% end %>

          <div class="hidden md:flex space-x-6">
            <%= link_to "Home", root_path, class: "text-gray-700 hover:text-blue-600" %>
            <%= link_to "Entities", entities_path, class: "text-gray-700 hover:text-blue-600" %>
            <%= link_to "Categories", categories_path, class: "text-gray-700 hover:text-blue-600" %>
          </div>

          <div class="flex items-center space-x-4">
            <% if user_signed_in? %>
              <%= link_to "Add Entity", new_entity_path, class: "bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700" %>
              <div class="relative group">
                <button class="flex items-center space-x-2 text-gray-700 hover:text-blue-600">
                  <span><%= current_user.full_name %></span>
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>
                <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 hidden group-hover:block">
                  <%= link_to "Profile", edit_user_registration_path, class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" %>
                  <%= link_to "Sign out", destroy_user_session_path, method: :delete, class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" %>
                </div>
              </div>
            <% else %>
              <%= link_to "Sign in", new_user_session_path, class: "text-gray-700 hover:text-blue-600" %>
              <%= link_to "Sign up", new_user_registration_path, class: "bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700" %>
            <% end %>
          </div>
        </div>
      </div>
    </nav>

    <!-- Flash Messages -->
    <% if notice %>
      <div class="fixed top-20 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-40" data-controller="flash">
        <%= notice %>
      </div>
    <% end %>

    <% if alert %>
      <div class="fixed top-20 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-40" data-controller="flash">
        <%= alert %>
      </div>
    <% end %>

    <!-- Main Content -->
    <main class="container mx-auto mt-24 px-4 pb-8">
      <%= yield %>
    </main>
  </body>
</html>
