<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "Cloudvision App" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= yield :head %>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Google Maps API -->
    <script async defer src="https://maps.googleapis.com/maps/api/js?key=<%= Rails.application.credentials.google_maps_api_key || 'YOUR_API_KEY' %>&libraries=places&callback=initMap"></script>
    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>
    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">
    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>
  <body>
    <!-- Professional Navigation -->
    <nav class="navbar">
      <div class="navbar-content">
        <div class="navbar-inner">
          <!-- Logo -->
          <%= link_to root_path, class: "logo" do %>
            CloudVision
          <% end %>
          <!-- Navigation Links -->
          <div class="nav-links">
            <%= link_to "Discover", root_path, class: "nav-link #{'active' if current_page?(root_path)}" %>
            <%= link_to "Places", entities_path, class: "nav-link #{'active' if current_page?(entities_path)}" %>
            <%= link_to "Categories", categories_path, class: "nav-link #{'active' if current_page?(categories_path)}" %>
            <% if user_signed_in? %>
              <%= link_to "Add Place", google_places_search_path, class: "nav-link #{'active' if current_page?(google_places_search_path)}" %>
            <% end %>
          </div>
          <!-- Auth Section -->
          <div class="flex items-center space-x-4">
            <% if user_signed_in? %>
              <div class="flex items-center space-x-3">
                <div class="flex items-center space-x-2">
                  <div class="w-8 h-8 bg-slate-900 rounded-full flex items-center justify-center">
                    <span class="text-white text-sm font-semibold">
                      <%= current_user.full_name.first.upcase %>
                    </span>
                  </div>
                  <span class="text-slate-700 font-medium"><%= current_user.full_name %></span>
                </div>
                <%= link_to "Sign Out", sign_out_path, method: :delete,
                    class: "btn btn-ghost btn-sm" %>
              </div>
            <% else %>
              <div class="flex items-center space-x-3">
                <%= link_to "Sign In", sign_in_path, class: "btn btn-ghost btn-sm" %>
                <%= link_to "Sign Up", sign_up_path, class: "btn btn-primary btn-sm" %>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </nav>
    <!-- Flash Messages -->
    <% if notice %>
      <div class="flash-message flash-success fade-in" data-controller="flash">
        <%= notice %>
      </div>
    <% end %>
    <% if alert %>
      <div class="flash-message flash-error fade-in" data-controller="flash">
        <%= alert %>
      </div>
    <% end %>
    <!-- Main Content -->
    <main class="main-content">
      <%= yield %>
    </main>
  </body>
</html>
