<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "Cloudvision App" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= yield :head %>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Google Maps API -->
    <script async defer src="https://maps.googleapis.com/maps/api/js?key=<%= Rails.application.credentials.google_maps_api_key || 'YOUR_API_KEY' %>&libraries=places&callback=initMap"></script>
    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>
    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">
    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>
  <body>
    <!-- Professional Navigation -->
    <nav class="navbar">
      <div class="navbar-content">
        <!-- Logo -->
        <%= link_to root_path, class: "logo" do %>
          CloudVision
        <% end %>
        <!-- Navigation Links -->
        <div class="nav-links">
          <%= link_to "Discover", root_path, class: "nav-link #{'active' if current_page?(root_path)}" %>
          <%= link_to "Places", entities_path, class: "nav-link #{'active' if current_page?(entities_path)}" %>
          <%= link_to "Categories", categories_path, class: "nav-link #{'active' if current_page?(categories_path)}" %>
          <% if user_signed_in? %>
            <%= link_to "Add Place", google_places_search_path, class: "nav-link #{'active' if current_page?(google_places_search_path)}" %>
          <% end %>
        </div>
        <!-- Auth Section -->
        <div style="display: flex; align-items: center; gap: 12px;">
          <% if user_signed_in? %>
            <div style="display: flex; align-items: center; gap: 12px;">
              <div style="display: flex; align-items: center; gap: 8px;">
                <div style="width: 32px; height: 32px; background: var(--primary); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                  <span style="color: white; font-size: 14px; font-weight: 600;">
                    <%= current_user.full_name.first.upcase %>
                  </span>
                </div>
                <span style="color: var(--foreground); font-weight: 500; font-size: 14px;"><%= current_user.full_name %></span>
              </div>
              <%= link_to "Sign Out", logout_path, method: :delete, class: "btn btn-ghost btn-sm" %>
            </div>
          <% else %>
            <div style="display: flex; align-items: center; gap: 12px;">
              <%= link_to "Sign In", sign_in_path, class: "btn btn-ghost btn-sm" %>
              <%= link_to "Sign Up", sign_up_path, class: "btn btn-primary btn-sm" %>
            </div>
          <% end %>
        </div>
      </div>
    </nav>
    <!-- Flash Messages -->
    <% if notice %>
      <div class="flash-message flash-success fade-in" data-controller="flash">
        <%= notice %>
      </div>
    <% end %>
    <% if alert %>
      <div class="flash-message flash-error fade-in" data-controller="flash">
        <%= alert %>
      </div>
    <% end %>
    <!-- Main Content -->
    <main class="main-content">
      <%= yield %>
    </main>
    <!-- Beautiful Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <!-- Brand Section -->
          <div class="footer-brand">
            <div class="footer-logo">
              <h3 style="font-size: 24px; font-weight: 700; color: var(--foreground); margin-bottom: 16px;">
                CloudVision
              </h3>
              <p style="color: var(--muted-foreground); line-height: 1.6; margin-bottom: 24px; max-width: 300px;">
                Discover exceptional places and share your experiences with a community of explorers. Find your next favorite spot today.
              </p>
              <div class="social-links">
                <a href="#" class="social-link" aria-label="Facebook">
                  <svg fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </a>
                <a href="#" class="social-link" aria-label="Twitter">
                  <svg fill="currentColor" viewBox="0 0 24 24">
                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                  </svg>
                </a>
                <a href="#" class="social-link" aria-label="Instagram">
                  <svg fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.781c-.315 0-.612-.123-.833-.344-.221-.221-.344-.518-.344-.833 0-.315.123-.612.344-.833.221-.221.518-.344.833-.344.315 0 .612.123.833.344.221.221.344.518.344.833 0 .315-.123.612-.344.833-.221.221-.518.344-.833.344z"/>
                  </svg>
                </a>
                <a href="#" class="social-link" aria-label="LinkedIn">
                  <svg fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </a>
              </div>
            </div>
          </div>
          <!-- Quick Links -->
          <div class="footer-section">
            <h4 class="footer-title">Quick Links</h4>
            <ul class="footer-links">
              <li><%= link_to "Home", root_path, class: "footer-link" %></li>
              <li><%= link_to "All Places", entities_path, class: "footer-link" %></li>
              <li><%= link_to "Categories", categories_path, class: "footer-link" %></li>
              <% if user_signed_in? %>
                <li><%= link_to "Add Place", google_places_search_path, class: "footer-link" %></li>
              <% else %>
                <li><%= link_to "Sign Up", sign_up_path, class: "footer-link" %></li>
              <% end %>
            </ul>
          </div>
          <!-- Categories -->
          <div class="footer-section">
            <h4 class="footer-title">Popular Categories</h4>
            <ul class="footer-links">
              <% Category.limit(5).each do |category| %>
                <li><%= link_to category.name, entities_path(category_id: category.id), class: "footer-link" %></li>
              <% end %>
            </ul>
          </div>
          <!-- Support -->
          <div class="footer-section">
            <h4 class="footer-title">Support</h4>
            <ul class="footer-links">
              <li><a href="#" class="footer-link">Help Center</a></li>
              <li><a href="#" class="footer-link">Contact Us</a></li>
              <li><a href="#" class="footer-link">Privacy Policy</a></li>
              <li><a href="#" class="footer-link">Terms of Service</a></li>
            </ul>
          </div>
        </div>
        <!-- Footer Bottom -->
        <div class="footer-bottom">
          <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 16px;">
            <p style="color: var(--muted-foreground); font-size: 14px; margin: 0;">
              © <%= Date.current.year %> CloudVision. All rights reserved.
            </p>
            <div style="display: flex; gap: 24px; flex-wrap: wrap;">
              <a href="#" class="footer-link" style="font-size: 14px;">Privacy</a>
              <a href="#" class="footer-link" style="font-size: 14px;">Terms</a>
              <a href="#" class="footer-link" style="font-size: 14px;">Cookies</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  </body>
</html>
