<div class="page-container" style="max-width: 520px; margin: 0 auto; padding-top: 120px;">
  <div class="card">
    <div class="card-content" style="padding: 40px;">
      <div style="text-align: center; margin-bottom: 32px;">
        <h1 class="page-title" style="margin-bottom: 8px;">Create Account</h1>
        <p class="page-subtitle">Join our community of explorers</p>
      </div>
      <%= form_with model: @user, url: sign_up_path, local: true do |form| %>
        <% if @user.errors.any? %>
          <div class="flash-message flash-error" style="position: static; margin-bottom: 24px; max-width: none;">
            <h3 style="font-weight: 600; margin-bottom: 8px;">Please fix the following errors:</h3>
            <ul style="margin: 0; padding-left: 16px;">
              <% @user.errors.full_messages.each do |message| %>
                <li style="margin-bottom: 4px;">• <%= message %></li>
              <% end %>
            </ul>
          </div>
        <% end %>
        <div class="grid grid-2" style="margin-bottom: 24px;">
          <div class="form-group">
            <%= form.label :first_name, class: "form-label" %>
            <%= form.text_field :first_name,
                placeholder: "First name",
                class: "form-input",
                required: true %>
          </div>
          <div class="form-group">
            <%= form.label :last_name, class: "form-label" %>
            <%= form.text_field :last_name,
                placeholder: "Last name",
                class: "form-input",
                required: true %>
          </div>
        </div>
        <div class="form-group">
          <%= form.label :email, class: "form-label" %>
          <%= form.email_field :email,
              placeholder: "Enter your email",
              class: "form-input",
              required: true %>
        </div>
        <div class="form-group">
          <%= form.label :password, class: "form-label" %>
          <%= form.password_field :password,
              placeholder: "Create a password",
              class: "form-input",
              required: true %>
        </div>
        <div class="form-group">
          <%= form.label :password_confirmation, class: "form-label" %>
          <%= form.password_field :password_confirmation,
              placeholder: "Confirm your password",
              class: "form-input",
              required: true %>
        </div>
        <div class="form-group">
          <%= form.label :bio, class: "form-label" %>
          <%= form.text_area :bio,
              placeholder: "Tell us a bit about yourself (optional)",
              rows: 3,
              class: "form-input form-textarea" %>
        </div>
        <div class="form-group">
          <%= form.submit "Create Account", class: "btn btn-primary btn-lg", style: "width: 100%;" %>
        </div>
      <% end %>
      <div style="text-align: center; margin-top: 24px; padding-top: 24px; border-top: 1px solid var(--border);">
        <p style="color: var(--muted-foreground);">
          Already have an account?
          <%= link_to "Sign in", sign_in_path, style: "color: var(--accent); font-weight: 600; text-decoration: none;" %>
        </p>
      </div>
    </div>
  </div>
