<div class="max-w-md mx-auto mt-8">
  <div class="bg-white rounded-lg shadow-md p-8">
    <h1 class="text-2xl font-bold text-gray-800 mb-6 text-center">Sign Up</h1>

    <%= form_with model: @user, url: sign_up_path, local: true, class: "space-y-4" do |form| %>
      <% if @user.errors.any? %>
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 class="text-red-800 font-semibold mb-2">Please fix the following errors:</h3>
          <ul class="text-red-700 text-sm space-y-1">
            <% @user.errors.full_messages.each do |message| %>
              <li>• <%= message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>

      <div class="grid grid-cols-2 gap-4">
        <div>
          <%= form.label :first_name, class: "block text-sm font-semibold text-gray-700 mb-2" %>
          <%= form.text_field :first_name,
              placeholder: "First name",
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
              required: true %>
        </div>

        <div>
          <%= form.label :last_name, class: "block text-sm font-semibold text-gray-700 mb-2" %>
          <%= form.text_field :last_name,
              placeholder: "Last name",
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
              required: true %>
        </div>
      </div>

      <div>
        <%= form.label :email, class: "block text-sm font-semibold text-gray-700 mb-2" %>
        <%= form.email_field :email,
            placeholder: "Enter your email",
            class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            required: true %>
      </div>

      <div>
        <%= form.label :password, class: "block text-sm font-semibold text-gray-700 mb-2" %>
        <%= form.password_field :password,
            placeholder: "Create a password",
            class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            required: true %>
      </div>

      <div>
        <%= form.label :password_confirmation, class: "block text-sm font-semibold text-gray-700 mb-2" %>
        <%= form.password_field :password_confirmation,
            placeholder: "Confirm your password",
            class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            required: true %>
      </div>

      <div>
        <%= form.label :bio, class: "block text-sm font-semibold text-gray-700 mb-2" %>
        <%= form.text_area :bio,
            placeholder: "Tell us a bit about yourself (optional)",
            rows: 3,
            class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none" %>
      </div>

      <div>
        <%= form.submit "Sign Up",
            class: "w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 font-semibold" %>
      </div>
    <% end %>

    <div class="mt-6 text-center">
      <p class="text-gray-600">
        Already have an account?
        <%= link_to "Sign in", sign_in_path, class: "text-blue-600 hover:text-blue-800 font-semibold" %>
      </p>
    </div>
  </div>
</div>
