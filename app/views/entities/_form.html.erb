<%= form_with model: entity, local: true, multipart: true do |form| %>
  <% if entity.errors.any? %>
    <div class="flash-message flash-error" style="position: static; margin-bottom: 24px; max-width: none;">
      <h3 style="font-weight: 600; margin-bottom: 8px;">Please fix the following errors:</h3>
      <ul style="margin: 0; padding-left: 16px;">
        <% entity.errors.full_messages.each do |message| %>
          <li style="margin-bottom: 4px;">• <%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>
  <div class="form-group">
    <%= form.label :name, class: "form-label" %>
    <%= form.text_field :name,
        placeholder: "Enter the name of the place",
        class: "form-input" %>
  </div>
  <div class="form-group">
    <%= form.label :category_id, "Category", class: "form-label" %>
    <%= form.select :category_id,
        options_from_collection_for_select(@categories, :id, :name, entity.category_id),
        { prompt: "Select a category" },
        { class: "form-input form-select" } %>
  </div>
  <div class="form-group">
    <%= form.label :description, class: "form-label" %>
    <%= form.text_area :description,
        placeholder: "Describe this place...",
        rows: 4,
        class: "form-input form-textarea" %>
  </div>
  <div class="form-group">
    <%= form.label :address, class: "form-label" %>
    <%= form.text_area :address,
        placeholder: "Enter the full address",
        rows: 2,
        class: "form-input form-textarea" %>
  </div>
  <div class="grid grid-2">
    <div class="form-group">
      <%= form.label :phone, class: "form-label" %>
      <%= form.telephone_field :phone,
          placeholder: "(*************",
          class: "form-input" %>
    </div>
    <div class="form-group">
      <%= form.label :email, class: "form-label" %>
      <%= form.email_field :email,
          placeholder: "<EMAIL>",
          class: "form-input" %>
    </div>
  </div>
  <div class="form-group">
    <%= form.label :website, class: "form-label" %>
    <%= form.url_field :website,
        placeholder: "https://example.com",
        class: "form-input" %>
  </div>
  <div class="grid grid-2">
    <div class="form-group">
      <%= form.label :latitude, class: "form-label" %>
      <%= form.number_field :latitude,
          step: :any,
          placeholder: "40.7128",
          class: "form-input" %>
      <p style="font-size: 12px; color: var(--muted-foreground); margin-top: 4px;">Optional: Latitude coordinate</p>
    </div>
    <div class="form-group">
      <%= form.label :longitude, class: "form-label" %>
      <%= form.number_field :longitude,
          step: :any,
          placeholder: "-74.0060",
          class: "form-input" %>
      <p style="font-size: 12px; color: var(--muted-foreground); margin-top: 4px;">Optional: Longitude coordinate</p>
    </div>
  </div>
  <div class="form-group">
    <%= form.label :images, class: "form-label" %>
    <%= form.file_field :images,
        multiple: true,
        accept: "image/*",
        class: "form-input" %>
    <p style="font-size: 12px; color: var(--muted-foreground); margin-top: 4px;">Optional: Upload images of this place</p>
  </div>
  <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 32px; padding-top: 24px; border-top: 1px solid var(--border);">
    <%= link_to "Cancel", entities_path, class: "btn btn-secondary btn-lg" %>
    <%= form.submit entity.persisted? ? "Update Place" : "Create Place", class: "btn btn-primary btn-lg" %>
  </div>
<% end %>
