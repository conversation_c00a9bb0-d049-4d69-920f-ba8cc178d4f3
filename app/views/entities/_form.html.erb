<%= form_with model: entity, local: true, multipart: true, class: "space-y-6" do |form| %>
  <% if entity.errors.any? %>
    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
      <h3 class="text-red-800 font-semibold mb-2">Please fix the following errors:</h3>
      <ul class="text-red-700 text-sm space-y-1">
        <% entity.errors.full_messages.each do |message| %>
          <li>• <%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>
  <div>
    <%= form.label :name, class: "block text-sm font-semibold text-gray-700 mb-2" %>
    <%= form.text_field :name,
        placeholder: "Enter the name of the place",
        class: "form-input" %>
  </div>
  <div>
    <%= form.label :category_id, "Category", class: "block text-sm font-semibold text-gray-700 mb-2" %>
    <%= form.select :category_id,
        options_from_collection_for_select(@categories, :id, :name, entity.category_id),
        { prompt: "Select a category" },
        { class: "form-input" } %>
  </div>
  <div>
    <%= form.label :description, class: "block text-sm font-semibold text-gray-700 mb-2" %>
    <%= form.text_area :description,
        placeholder: "Describe this place...",
        rows: 4,
        class: "form-input resize-none" %>
  </div>
  <div>
    <%= form.label :address, class: "block text-sm font-semibold text-gray-700 mb-2" %>
    <%= form.text_area :address, 
        placeholder: "Enter the full address", 
        rows: 2,
        class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none" %>
  </div>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div>
      <%= form.label :phone, class: "block text-sm font-semibold text-gray-700 mb-2" %>
      <%= form.telephone_field :phone, 
          placeholder: "(*************", 
          class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" %>
    </div>
    <div>
      <%= form.label :email, class: "block text-sm font-semibold text-gray-700 mb-2" %>
      <%= form.email_field :email, 
          placeholder: "<EMAIL>", 
          class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" %>
    </div>
  </div>
  <div>
    <%= form.label :website, class: "block text-sm font-semibold text-gray-700 mb-2" %>
    <%= form.url_field :website, 
        placeholder: "https://example.com", 
        class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" %>
  </div>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div>
      <%= form.label :latitude, class: "block text-sm font-semibold text-gray-700 mb-2" %>
      <%= form.number_field :latitude, 
          step: :any,
          placeholder: "40.7128", 
          class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" %>
      <p class="text-xs text-gray-500 mt-1">Optional: Latitude coordinate</p>
    </div>
    <div>
      <%= form.label :longitude, class: "block text-sm font-semibold text-gray-700 mb-2" %>
      <%= form.number_field :longitude, 
          step: :any,
          placeholder: "-74.0060", 
          class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" %>
      <p class="text-xs text-gray-500 mt-1">Optional: Longitude coordinate</p>
    </div>
  </div>
  <div>
    <%= form.label :images, class: "block text-sm font-semibold text-gray-700 mb-2" %>
    <%= form.file_field :images, 
        multiple: true,
        accept: "image/*",
        class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" %>
    <p class="text-xs text-gray-500 mt-1">Optional: Upload images of this place</p>
  </div>
  <div class="flex justify-end space-x-4">
    <%= link_to "Cancel", entities_path, class: "px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 font-semibold" %>
    <%= form.submit entity.persisted? ? "Update Place" : "Create Place", 
        class: "bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-semibold" %>
  </div>
<% end %>
