<div class="flex justify-between items-center mb-8">
  <h1 class="text-3xl font-bold text-gray-800">All Places</h1>
  <% if user_signed_in? %>
    <%= link_to "Add New Place", new_entity_path, class: "bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-semibold" %>
  <% end %>
</div>

<!-- Filters -->
<div class="bg-white rounded-lg shadow-sm p-6 mb-8">
  <div class="flex flex-wrap items-center gap-4">
    <%= link_to "All Categories", entities_path,
        class: "px-4 py-2 rounded-lg #{params[:category_id].blank? ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}" %>

    <% @categories.each do |category| %>
      <%= link_to category.name, entities_path(category_id: category.id),
          class: "px-4 py-2 rounded-lg #{params[:category_id] == category.id.to_s ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}" %>
    <% end %>
  </div>
</div>

<!-- Entities Grid -->
<% if @entities.any? %>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
    <% @entities.each do |entity| %>
      <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
        <div class="h-48 bg-gray-200 flex items-center justify-center">
          <span class="text-4xl">
            <%= case entity.category.name
                when "Restaurants" then "🍽️"
                when "Shops" then "🛍️"
                when "Entertainment" then "🎬"
                when "Hotels" then "🏨"
                when "Services" then "🔧"
                when "Health & Fitness" then "💪"
                when "Education" then "📚"
                when "Automotive" then "🚗"
                else "📍"
                end %>
          </span>
        </div>

        <div class="p-6">
          <h3 class="font-semibold text-xl mb-2">
            <%= link_to entity.name, entity_path(entity), class: "text-gray-800 hover:text-blue-600" %>
          </h3>

          <p class="text-gray-600 text-sm mb-3"><%= truncate(entity.description, length: 120) %></p>

          <div class="flex items-center justify-between mb-3">
            <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
              <%= entity.category.name %>
            </span>

            <div class="flex items-center">
              <div class="flex text-yellow-400 mr-1">
                <% 5.times do |i| %>
                  <svg class="w-4 h-4 <%= i < entity.average_rating ? 'text-yellow-400' : 'text-gray-300' %>" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                <% end %>
              </div>
              <span class="text-sm text-gray-600">(<%= entity.total_reviews %>)</span>
            </div>
          </div>

          <% if entity.address.present? %>
            <p class="text-gray-500 text-sm flex items-center">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
              </svg>
              <%= truncate(entity.address, length: 40) %>
            </p>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Pagination -->
  <%= paginate @entities, theme: 'twitter-bootstrap-4' if respond_to?(:paginate) %>
<% else %>
  <div class="text-center py-12">
    <div class="text-6xl mb-4">🏪</div>
    <h3 class="text-xl font-semibold text-gray-800 mb-2">No places found</h3>
    <p class="text-gray-600 mb-6">
      <% if params[:category_id].present? %>
        No places found in this category.
      <% else %>
        Be the first to add a place to our directory!
      <% end %>
    </p>
    <% if user_signed_in? %>
      <%= link_to "Add the First Place", new_entity_path, class: "bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-semibold" %>
    <% end %>
  </div>
<% end %>
