<%= turbo_stream_from "entity_#{@entity.id}_reviews" %>
<div class="page-container" style="max-width: 1000px; margin: 0 auto;">
  <!-- Header -->
  <div class="card" style="margin-bottom: 32px;">
    <div style="height: 200px; background: linear-gradient(135deg, var(--secondary) 0%, var(--muted) 100%); display: flex; align-items: center; justify-content: center; border-radius: var(--radius) var(--radius) 0 0;">
      <span style="font-size: 80px;">
        <%= case @entity.category.name
            when "Restaurants" then "🍽️"
            when "Shops" then "🛍️"
            when "Entertainment" then "🎬"
            when "Hotels" then "🏨"
            when "Services" then "🔧"
            when "Health & Fitness" then "💪"
            when "Education" then "📚"
            when "Automotive" then "🚗"
            else "📍"
            end %>
      </span>
    </div>
    <div class="card-content" style="padding: 32px;">
      <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 24px;">
        <div>
          <h1 class="page-title" style="margin-bottom: 12px;"><%= @entity.name %></h1>
          <span class="badge badge-primary">
            <%= @entity.category.name %>
          </span>
        </div>
        <% if current_user == @entity.user %>
          <div style="display: flex; gap: 8px;">
            <%= link_to "Edit", edit_entity_path(@entity), class: "btn btn-secondary btn-sm" %>
            <%= link_to "Delete", entity_path(@entity), method: :delete,
                confirm: "Are you sure?",
                class: "btn btn-destructive btn-sm" %>
          </div>
        <% end %>
      </div>
      <!-- Rating Summary -->
      <div class="flex items-center mb-6">
        <div class="flex text-yellow-400 mr-3">
          <% 5.times do |i| %>
            <svg class="w-6 h-6 <%= i < @entity.average_rating ? 'text-yellow-400' : 'text-gray-300' %>" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
          <% end %>
        </div>
        <span class="text-xl font-semibold text-gray-800 mr-2"><%= @entity.average_rating %></span>
        <span class="text-gray-600">(<%= pluralize(@entity.total_reviews, 'review') %>)</span>
      </div>
      <!-- Description -->
      <% if @entity.description.present? %>
        <p class="text-gray-700 mb-6 leading-relaxed"><%= @entity.description %></p>
      <% end %>
      <!-- Contact Information -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <% if @entity.address.present? %>
          <div class="flex items-start">
            <svg class="w-5 h-5 text-gray-400 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
            <div>
              <h3 class="font-semibold text-gray-800">Address</h3>
              <p class="text-gray-600"><%= @entity.address %></p>
            </div>
          </div>
        <% end %>
        <% if @entity.phone.present? %>
          <div class="flex items-start">
            <svg class="w-5 h-5 text-gray-400 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
            </svg>
            <div>
              <h3 class="font-semibold text-gray-800">Phone</h3>
              <p class="text-gray-600"><%= @entity.phone %></p>
            </div>
          </div>
        <% end %>
        <% if @entity.website.present? %>
          <div class="flex items-start">
            <svg class="w-5 h-5 text-gray-400 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"/>
            </svg>
            <div>
              <h3 class="font-semibold text-gray-800">Website</h3>
              <%= link_to @entity.website, @entity.website, target: "_blank", class: "text-blue-600 hover:text-blue-800" %>
            </div>
          </div>
        <% end %>
        <% if @entity.email.present? %>
          <div class="flex items-start">
            <svg class="w-5 h-5 text-gray-400 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
            </svg>
            <div>
              <h3 class="font-semibold text-gray-800">Email</h3>
              <%= link_to @entity.email, "mailto:#{@entity.email}", class: "text-blue-600 hover:text-blue-800" %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
  <!-- Reviews Section -->
  <div class="bg-white rounded-lg shadow-md p-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Reviews</h2>
    <!-- Add Review Form -->
    <% if user_signed_in? %>
      <div id="review_form" class="mb-8">
        <%= render 'reviews/form', review: @review, entity: @entity %>
      </div>
    <% else %>
      <div class="bg-gray-50 rounded-lg p-6 mb-8 text-center">
        <p class="text-gray-600 mb-4">Sign in to leave a review</p>
        <%= link_to "Sign In", sign_in_path, class: "bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700" %>
      </div>
    <% end %>
    <!-- Reviews List -->
    <div id="reviews">
      <% if @reviews.any? %>
        <% @reviews.each do |review| %>
          <%= render 'reviews/review', review: review %>
        <% end %>
        <!-- Pagination -->
        <%= paginate @reviews if respond_to?(:paginate) %>
      <% else %>
        <div class="text-center py-8">
          <div class="text-4xl mb-4">💬</div>
          <h3 class="text-lg font-semibold text-gray-800 mb-2">No reviews yet</h3>
          <p class="text-gray-600">Be the first to leave a review!</p>
        </div>
      <% end %>
    </div>
  </div>
</div>
