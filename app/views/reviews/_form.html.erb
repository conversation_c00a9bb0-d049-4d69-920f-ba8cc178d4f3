<%= form_with model: [entity, review], local: false, class: "space-y-4" do |form| %>
  <% if review.errors.any? %>
    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
      <h3 class="text-red-800 font-semibold mb-2">Please fix the following errors:</h3>
      <ul class="text-red-700 text-sm space-y-1">
        <% review.errors.full_messages.each do |message| %>
          <li>• <%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <label class="block text-sm font-semibold text-gray-700 mb-2">Rating</label>
    <div data-controller="star-rating" data-star-rating-rating-value="<%= review.rating || 0 %>">
      <%= form.hidden_field :rating, data: { star_rating_target: "input" } %>
      <div class="flex space-x-1">
        <% 5.times do |i| %>
          <button type="button" 
                  data-rating="<%= i + 1 %>"
                  data-action="click->star-rating#rate mouseover->star-rating#hover mouseleave->star-rating#leave"
                  data-star-rating-target="star"
                  class="text-2xl text-gray-300 hover:text-yellow-400 focus:outline-none transition-colors">
            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
          </button>
        <% end %>
      </div>
    </div>
  </div>

  <div>
    <%= form.label :comment, class: "block text-sm font-semibold text-gray-700 mb-2" %>
    <%= form.text_area :comment, 
        placeholder: "Share your experience...", 
        rows: 4,
        class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none" %>
  </div>

  <div class="flex justify-end">
    <%= form.submit review.persisted? ? "Update Review" : "Submit Review", 
        class: "bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-semibold" %>
  </div>
<% end %>
