<%= form_with model: [entity, review], local: false do |form| %>
  <% if review.errors.any? %>
    <div class="flash-message flash-error" style="position: static; margin-bottom: 24px; max-width: none;">
      <h3 style="font-weight: 600; margin-bottom: 8px;">Please fix the following errors:</h3>
      <ul style="margin: 0; padding-left: 16px;">
        <% review.errors.full_messages.each do |message| %>
          <li style="margin-bottom: 4px;">• <%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>
  <div class="form-group">
    <label class="form-label">Rating</label>
    <div data-controller="star-rating" data-star-rating-rating-value="<%= review.rating || 0 %>">
      <%= form.hidden_field :rating, data: { star_rating_target: "input" } %>
      <div style="display: flex; gap: 4px;">
        <% 5.times do |i| %>
          <button type="button"
                  data-rating="<%= i + 1 %>"
                  data-action="click->star-rating#rate mouseover->star-rating#hover mouseleave->star-rating#leave"
            data-star-rating-target="star"
            style="background: none; border: none; padding: 0; cursor: pointer; color: #d1d5db; font-size: 32px; transition: color 0.15s ease;"
            onmouseover="this.style.color='#f59e0b'"
            onmouseout="this.style.color='#d1d5db'">
            <svg style="width: 32px; height: 32px;" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
          </button>
        <% end %>
      </div>
    </div>
  </div>
  <div class="form-group">
    <%= form.label :comment, class: "form-label" %>
    <%= form.text_area :comment,
        placeholder: "Share your experience...",
        rows: 4,
        class: "form-input form-textarea" %>
  </div>
  <div style="display: flex; justify-content: flex-end; margin-top: 24px;">
    <%= form.submit review.persisted? ? "Update Review" : "Submit Review", class: "btn btn-primary" %>
  </div>
<% end %>
