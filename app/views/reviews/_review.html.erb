<div id="review_<%= review.id %>" class="bg-white rounded-lg shadow-sm border p-4 mb-4">
  <div class="flex items-start justify-between">
    <div class="flex-1">
      <div class="flex items-center mb-2">
        <div class="flex text-yellow-400 mr-2">
          <% 5.times do |i| %>
            <svg class="w-4 h-4 <%= i < review.rating ? 'text-yellow-400' : 'text-gray-300' %>" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
          <% end %>
        </div>
        <span class="font-semibold text-gray-800"><%= review.user.full_name %></span>
      </div>
      
      <% if review.comment.present? %>
        <p class="text-gray-700 mb-2"><%= review.comment %></p>
      <% end %>
    </div>
    
    <div class="flex items-center space-x-2">
      <span class="text-sm text-gray-500"><%= time_ago_in_words(review.created_at) %> ago</span>
      
      <% if current_user == review.user %>
        <div class="flex space-x-1">
          <%= link_to "Edit", edit_entity_review_path(review.entity, review), 
              class: "text-blue-600 hover:text-blue-800 text-sm",
              data: { turbo_frame: "review_#{review.id}" } %>
          <%= link_to "Delete", entity_review_path(review.entity, review), 
              method: :delete,
              class: "text-red-600 hover:text-red-800 text-sm",
              confirm: "Are you sure?",
              data: { turbo_method: :delete } %>
        </div>
      <% end %>
    </div>
  </div>
</div>
