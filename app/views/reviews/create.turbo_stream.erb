<%= turbo_stream.replace "review_form" do %>
  <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
    <p class="text-green-800 font-semibold">Thank you for your review!</p>
  </div>
  
  <% if current_user %>
    <% existing_review = @entity.reviews.find_by(user: current_user) %>
    <% if existing_review %>
      <%= render 'reviews/form', review: existing_review, entity: @entity %>
    <% end %>
  <% end %>
<% end %>

<%= turbo_stream.update "reviews" do %>
  <% if @entity.reviews.any? %>
    <% @entity.reviews.includes(:user).recent.each do |review| %>
      <%= render 'reviews/review', review: review %>
    <% end %>
  <% else %>
    <div class="text-center py-8">
      <div class="text-4xl mb-4">💬</div>
      <h3 class="text-lg font-semibold text-gray-800 mb-2">No reviews yet</h3>
      <p class="text-gray-600">Be the first to leave a review!</p>
    </div>
  <% end %>
<% end %>
