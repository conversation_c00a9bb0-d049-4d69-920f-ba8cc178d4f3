class User < ApplicationRecord
  has_secure_password

  # Associations
  has_many :entities, dependent: :destroy
  has_many :reviews, dependent: :destroy
  has_one_attached :avatar

  # Validations
  validates :email, presence: true, uniqueness: { case_sensitive: false }
  validates :first_name, presence: true, length: { maximum: 50 }
  validates :last_name, presence: true, length: { maximum: 50 }
  validates :bio, length: { maximum: 500 }

  # Normalize email before saving
  before_save { self.email = email.downcase.strip }

  # Methods
  def full_name
    "#{first_name} #{last_name}".strip
  end

  def initials
    "#{first_name&.first}#{last_name&.first}".upcase
  end
end
