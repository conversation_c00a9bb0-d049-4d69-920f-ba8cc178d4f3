class Review < ApplicationRecord
  # Associations
  belongs_to :user
  belongs_to :entity

  # Validations
  validates :rating, presence: true, inclusion: { in: 1..5, message: "must be between 1 and 5" }
  validates :comment, length: { maximum: 1000 }
  validates :user_id, uniqueness: { scope: :entity_id, message: "You can only review an entity once" }

  # Scopes
  scope :recent, -> { order(created_at: :desc) }
  scope :by_rating, ->(rating) { where(rating: rating) }
  scope :with_comments, -> { where.not(comment: [ nil, "" ]) }

  # Callbacks
  after_create :broadcast_new_review, unless: :skip_broadcasting?
  after_update :broadcast_updated_review, unless: :skip_broadcasting?
  after_destroy :broadcast_removed_review, unless: :skip_broadcasting?

  private

  def skip_broadcasting?
    # Skip broadcasting during seeding or when there's no request context
    Rails.env.test? || !defined?(ActionController::Base) || !ActionController::Base.current.present?
  end

  def broadcast_new_review
    broadcast_prepend_to "entity_#{entity.id}_reviews",
                         target: "reviews",
                         partial: "reviews/review",
                         locals: { review: self }
  end

  def broadcast_updated_review
    broadcast_replace_to "entity_#{entity.id}_reviews",
                         target: "review_#{id}",
                         partial: "reviews/review",
                         locals: { review: self }
  end

  def broadcast_removed_review
    broadcast_remove_to "entity_#{entity.id}_reviews",
                        target: "review_#{id}"
  end
end
