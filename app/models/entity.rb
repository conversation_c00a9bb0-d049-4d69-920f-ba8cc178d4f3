class Entity < ApplicationRecord
  include PgSearch::Model

  # Associations
  belongs_to :category
  belongs_to :user
  has_many :reviews, dependent: :destroy
  has_many_attached :images

  # Search configuration
  pg_search_scope :search_by_name_and_description,
                  against: [ :name, :description, :address ],
                  using: {
                    tsearch: { prefix: true }
                  }

  # Validations
  validates :name, presence: true, length: { maximum: 100 }
  validates :description, length: { maximum: 1000 }
  validates :address, length: { maximum: 500 }
  validates :phone, length: { maximum: 20 }
  validates :website, format: { with: URI::DEFAULT_PARSER.make_regexp(%w[http https]), message: "must be a valid URL" }, allow_blank: true
  validates :email, format: { with: URI::MailTo::EMAIL_REGEXP }, allow_blank: true
  validates :latitude, :longitude, numericality: true, allow_blank: true

  # Scopes
  scope :active, -> { where(active: true) }
  scope :by_category, ->(category) { where(category: category) }
  scope :recent, -> { order(created_at: :desc) }

  # Methods
  def average_rating
    return 0 if reviews.empty?
    reviews.average(:rating).to_f.round(1)
  end

  def total_reviews
    reviews.count
  end

  def to_s
    name
  end
end
