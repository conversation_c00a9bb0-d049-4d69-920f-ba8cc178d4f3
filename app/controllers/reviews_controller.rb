class ReviewsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_entity
  before_action :set_review, only: [ :edit, :update, :destroy ]
  before_action :check_owner, only: [ :edit, :update, :destroy ]

  def create
    @review = @entity.reviews.build(review_params)
    @review.user = current_user

    respond_to do |format|
      if @review.save
        format.turbo_stream
        format.html { redirect_to @entity, notice: "Review was successfully created." }
      else
        format.turbo_stream { render turbo_stream: turbo_stream.replace("review_form", partial: "reviews/form", locals: { review: @review, entity: @entity }) }
        format.html { redirect_to @entity, alert: @review.errors.full_messages.join(", ") }
      end
    end
  end

  def edit
    respond_to do |format|
      format.turbo_stream
      format.html
    end
  end

  def update
    respond_to do |format|
      if @review.update(review_params)
        format.turbo_stream
        format.html { redirect_to @entity, notice: "Review was successfully updated." }
      else
        format.turbo_stream { render turbo_stream: turbo_stream.replace("review_#{@review.id}", partial: "reviews/edit_form", locals: { review: @review, entity: @entity }) }
        format.html { render :edit, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @review.destroy

    respond_to do |format|
      format.turbo_stream
      format.html { redirect_to @entity, notice: "Review was successfully deleted." }
    end
  end

  private

  def set_entity
    @entity = Entity.find(params[:entity_id])
  end

  def set_review
    @review = @entity.reviews.find(params[:id])
  end

  def check_owner
    redirect_to @entity, alert: "You can only edit your own reviews." unless @review.user == current_user
  end

  def review_params
    params.require(:review).permit(:rating, :comment)
  end
end
