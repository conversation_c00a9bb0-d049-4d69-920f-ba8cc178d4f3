class EntitiesController < ApplicationController
  before_action :authenticate_user!, except: [ :index, :show, :search ]
  before_action :set_entity, only: [ :show, :edit, :update, :destroy ]
  before_action :check_owner, only: [ :edit, :update, :destroy ]

  def index
    @entities = Entity.active.includes(:category, :reviews)
    @entities = @entities.by_category(params[:category_id]) if params[:category_id].present?
    @entities = @entities.page(params[:page]).per(12)
    @categories = Category.order(:name)
  end

  def show
    @review = current_user ? @entity.reviews.find_or_initialize_by(user: current_user) : Review.new
    @reviews = @entity.reviews.includes(:user).recent.page(params[:page]).per(10)
  end

  def new
    @entity = current_user.entities.build
    @categories = Category.order(:name)
  end

  def create
    @entity = current_user.entities.build(entity_params)
    @categories = Category.order(:name)

    if @entity.save
      redirect_to @entity, notice: "Entity was successfully created."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    @categories = Category.order(:name)
  end

  def update
    @categories = Category.order(:name)

    if @entity.update(entity_params)
      redirect_to @entity, notice: "Entity was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @entity.destroy
    redirect_to entities_url, notice: "Entity was successfully deleted."
  end

  def search
    if params[:q].present?
      @entities = Entity.active.search_by_name_and_description(params[:q])
                        .includes(:category, :reviews)
                        .page(params[:page]).per(12)
    else
      @entities = Entity.none.page(params[:page])
    end
    @query = params[:q]
  end

  private

  def set_entity
    @entity = Entity.find(params[:id])
  end

  def check_owner
    redirect_to entities_path, alert: "You can only edit your own entities." unless @entity.user == current_user
  end

  def entity_params
    params.require(:entity).permit(:name, :description, :address, :phone, :website, :email,
                                   :latitude, :longitude, :category_id, images: [])
  end
end
