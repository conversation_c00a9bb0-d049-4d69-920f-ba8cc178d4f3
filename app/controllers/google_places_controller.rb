class GooglePlacesController < ApplicationController
  before_action :authenticate_user!

  def search
    @places = []
    @query = params[:q]
    @location = params[:location] || "Thimphu, Bhutan" # Default location

    if @query.present?
      service = GooglePlacesService.new
      @places = service.search_places_by_text(@query, @location)
    end

    respond_to do |format|
      format.html
      format.json { render json: @places }
    end
  end


  def import
    place_data_json = params[:place_data]

    if place_data_json.present?
      begin
        place_data = JSON.parse(place_data_json).with_indifferent_access

        # Map Google Places types to our categories
        category = map_google_type_to_category(place_data[:types])

        entity = current_user.entities.build(
          name: place_data[:name],
          description: "Imported from Google Places",
          address: place_data[:address],
          phone: place_data[:phone],
          website: place_data[:website],
          latitude: place_data[:latitude],
          longitude: place_data[:longitude],
          category: category
        )

        if entity.save
          redirect_to entity, notice: "Place imported successfully!"
        else
          redirect_back(fallback_location: root_path, alert: "Failed to import place: #{entity.errors.full_messages.join(', ')}")
        end
      rescue JSON::ParserError
        redirect_back(fallback_location: root_path, alert: "Invalid place data format")
      end
    else
      redirect_back(fallback_location: root_path, alert: "No place data provided")
    end
  end

  private

  def map_google_type_to_category(types)
    return Category.first unless types.present?

    # Map Google Places types to our categories
    category_mapping = {
      "restaurant" => "Restaurants",
      "food" => "Restaurants",
      "meal_takeaway" => "Restaurants",
      "cafe" => "Restaurants",
      "store" => "Shops",
      "shopping_mall" => "Shops",
      "clothing_store" => "Shops",
      "electronics_store" => "Shops",
      "movie_theater" => "Entertainment",
      "amusement_park" => "Entertainment",
      "night_club" => "Entertainment",
      "lodging" => "Hotels",
      "gym" => "Health & Fitness",
      "spa" => "Health & Fitness",
      "hospital" => "Health & Fitness",
      "school" => "Education",
      "university" => "Education",
      "car_dealer" => "Automotive",
      "car_repair" => "Automotive",
      "gas_station" => "Automotive"
    }

    # Find the first matching category
    matching_type = types.find { |type| category_mapping.key?(type) }
    category_name = category_mapping[matching_type] if matching_type

    Category.find_by(name: category_name) || Category.find_by(name: "Services") || Category.first
  end
end
