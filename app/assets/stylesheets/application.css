@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/*
 * CloudVision - Professional Design System
 * Modern, clean, and sophisticated
 */

:root {
  --primary: #0f172a;
  --primary-foreground: #f8fafc;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --accent: #3b82f6;
  --accent-foreground: #ffffff;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --border: #e2e8f0;
  --input: #ffffff;
  --ring: #3b82f6;
  --background: #ffffff;
  --foreground: #0f172a;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --popover: #ffffff;
  --popover-foreground: #0f172a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --radius: 0.75rem;
}

* {
  border-color: var(--border);
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  background-color: var(--background);
  color: var(--foreground);
  font-feature-settings: 'rlig' 1, 'calt' 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Modern Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border);
  height: 72px;
}

.navbar-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  font-size: 24px;
  font-weight: 800;
  color: var(--primary);
  text-decoration: none;
  letter-spacing: -0.025em;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 32px;
}

.nav-link {
  color: var(--muted-foreground);
  text-decoration: none;
  font-weight: 500;
  font-size: 15px;
  transition: color 0.2s ease;
  position: relative;
}

.nav-link:hover {
  color: var(--foreground);
}

.nav-link.active {
  color: var(--foreground);
  font-weight: 600;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--accent);
  border-radius: 1px;
}

/* Modern Cards */
.card {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.card:hover {
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  transform: translateY(-4px);
  border-color: rgba(59, 130, 246, 0.2);
}

.card-header {
  padding: 24px;
  padding-bottom: 16px;
}

.card-content {
  padding: 0 24px 24px 24px;
}

.card-footer {
  padding: 16px 24px;
  background: var(--muted);
  border-top: 1px solid var(--border);
}

/* Modern Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius);
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--ring);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-sm {
  padding: 8px 16px;
  font-size: 13px;
}

.btn-md {
  padding: 12px 24px;
  font-size: 14px;
}

.btn-lg {
  padding: 16px 32px;
  font-size: 16px;
}

.btn-primary {
  background: var(--primary);
  color: var(--primary-foreground);
}

.btn-primary:hover {
  background: #1e293b;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(15, 23, 42, 0.4);
}

.btn-secondary {
  background: var(--secondary);
  color: var(--secondary-foreground);
  border: 1px solid var(--border);
}

.btn-secondary:hover {
  background: #e2e8f0;
  transform: translateY(-1px);
}

.btn-ghost {
  background: transparent;
  color: var(--muted-foreground);
}

.btn-ghost:hover {
  background: var(--muted);
  color: var(--foreground);
}

/* Modern Forms */
.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  font-weight: 600;
  font-size: 14px;
  color: var(--foreground);
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  background: var(--input);
  color: var(--foreground);
  font-size: 14px;
  transition: all 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder {
  color: var(--muted-foreground);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

/* Modern Hero Section */
.hero {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 120px 0 80px 0;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.5;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: clamp(32px, 5vw, 64px);
  font-weight: 800;
  color: var(--primary);
  line-height: 1.1;
  letter-spacing: -0.02em;
  margin-bottom: 24px;
}

.hero-subtitle {
  font-size: 20px;
  color: var(--muted-foreground);
  max-width: 600px;
  margin: 0 auto 40px auto;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Modern Search */
.search-container {
  max-width: 600px;
  margin: 0 auto;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 16px 20px 16px 52px;
  font-size: 16px;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  background: var(--input);
  color: var(--foreground);
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.search-input:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--muted-foreground);
  width: 20px;
  height: 20px;
}

/* Modern Ratings */
.rating-stars {
  display: flex;
  align-items: center;
  gap: 4px;
}

.rating-star {
  width: 20px;
  height: 20px;
  color: #d1d5db;
  cursor: pointer;
  transition: color 0.15s ease;
}

.rating-star.active {
  color: #fbbf24;
}

.rating-star:hover {
  color: #f59e0b;
}

/* Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.main-content {
  padding-top: 72px;
  min-height: 100vh;
}

.section {
  padding: 80px 0;
}

.section-header {
  text-align: center;
  margin-bottom: 64px;
}

.section-title {
  font-size: clamp(28px, 4vw, 48px);
  font-weight: 800;
  color: var(--primary);
  line-height: 1.2;
  letter-spacing: -0.02em;
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 18px;
  color: var(--muted-foreground);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Page containers for consistent spacing */
.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 24px;
}

.page-header {
  margin-bottom: 40px;
}

.page-title {
  font-size: clamp(24px, 3vw, 36px);
  font-weight: 800;
  color: var(--primary);
  margin-bottom: 12px;
}

.page-subtitle {
  font-size: 16px;
  color: var(--muted-foreground);
  line-height: 1.6;
}

/* Modern Grid */
.grid {
  display: grid;
  gap: 24px;
}

.grid-1 { grid-template-columns: 1fr; }
.grid-2 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
.grid-3 { grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); }
.grid-4 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

/* Flash Messages */
.flash-message {
  position: fixed;
  top: 88px;
  right: 24px;
  z-index: 50;
  max-width: 400px;
  padding: 16px 20px;
  border-radius: var(--radius);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
  font-weight: 500;
  animation: fadeIn 0.3s ease-out;
}

.flash-success {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.flash-error {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

/* Content spacing utilities */
.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.section-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Responsive */
@media (max-width: 768px) {
  .navbar-content {
    padding: 0 16px;
  }

  .container {
    padding: 0 16px;
  }

  .content-wrapper {
    padding: 0 16px;
  }

  .section-content {
    padding: 0 16px;
  }

  .page-container {
    padding: 24px 16px;
  }

  .nav-links {
    display: none;
  }

  .hero {
    padding: 80px 0 60px 0;
  }

  .section {
    padding: 60px 0;
  }

  .section-header {
    margin-bottom: 40px;
  }

  .page-header {
    margin-bottom: 24px;
  }
}