@import "tailwindcss/preflight";
@import "tailwindcss/utilities";

/*
 * CloudVision App - Custom Styles with Tailwind CSS
 */

@layer base {
  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    min-height: 100vh;
  }
}

@layer components {
  /* Navigation */
  nav {
    @apply bg-white/95 backdrop-blur-sm shadow-lg fixed top-0 left-0 right-0 z-50;
  }

  /* Cards */
  .card {
    @apply bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 hover:scale-105;
  }

  /* Buttons */
  .btn {
    @apply px-6 py-3 rounded-lg font-semibold transition-all duration-200 relative overflow-hidden;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
  }

  /* Form inputs */
  .form-input {
    @apply w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-200;
  }

  /* Glass morphism */
  .glass {
    @apply bg-white/10 backdrop-blur-lg border border-white/20;
  }

  /* Hero gradient */
  .hero-gradient {
    @apply bg-gradient-to-br from-blue-600 to-purple-600;
  }

  /* Animations */
  .float-animation {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }

  /* Star ratings */
  .star-rating .star {
    @apply transition-all duration-200 cursor-pointer text-gray-300 hover:text-yellow-400;
  }

  .star-rating .star.active {
    @apply text-yellow-400;
  }

  /* Main content spacing */
  main {
    @apply mt-24 pb-8;
  }

  /* Container */
  .container {
    @apply max-w-7xl mx-auto px-4;
  }

  /* Flash messages */
  .flash-message {
    @apply fixed top-20 right-4 z-40 px-6 py-3 rounded-lg text-white font-semibold shadow-lg;
  }

  .flash-notice {
    @apply bg-green-500;
  }

  .flash-alert {
    @apply bg-red-500;
  }
}