@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/*
 * CloudVision - Professional Rating Platform
 * Modern, sophisticated design system
 */

@layer base {
  * {
    @apply border-border;
  }

  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    @apply bg-slate-50 text-slate-900 antialiased;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Professional Navigation */
  .navbar {
    @apply fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-xl border-b border-slate-200/60;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  }

  .navbar-content {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .navbar-inner {
    @apply flex justify-between items-center h-16;
  }

  .logo {
    @apply text-xl font-bold text-slate-900 tracking-tight;
    font-weight: 800;
  }

  .nav-links {
    @apply hidden md:flex items-center space-x-8;
  }

  .nav-link {
    @apply text-slate-600 hover:text-slate-900 px-3 py-2 text-sm font-medium transition-colors duration-200;
  }

  .nav-link.active {
    @apply text-slate-900 font-semibold;
  }

  /* Professional Cards */
  .card {
    @apply bg-white rounded-2xl border border-slate-200/60 overflow-hidden;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card:hover {
    @apply border-slate-300/60;
    box-shadow: 0 10px 25px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    transform: translateY(-2px);
  }

  .card-header {
    @apply p-6 pb-4;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply px-6 py-4 bg-slate-50/50 border-t border-slate-100;
  }

  /* Professional Buttons */
  .btn {
    @apply inline-flex items-center justify-center rounded-xl text-sm font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }

  .btn-sm {
    @apply px-3 py-2 text-xs;
  }

  .btn-md {
    @apply px-4 py-2.5;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  .btn-primary {
    @apply bg-slate-900 text-white hover:bg-slate-800 focus:ring-slate-900;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  }

  .btn-secondary {
    @apply bg-white text-slate-900 border border-slate-300 hover:bg-slate-50 focus:ring-slate-500;
  }

  .btn-ghost {
    @apply text-slate-600 hover:text-slate-900 hover:bg-slate-100;
  }

  /* Professional Forms */
  .form-group {
    @apply space-y-2;
  }

  .form-label {
    @apply block text-sm font-semibold text-slate-900;
  }

  .form-input {
    @apply block w-full rounded-xl border border-slate-300 px-4 py-3 text-slate-900 placeholder-slate-500 focus:border-slate-900 focus:outline-none focus:ring-1 focus:ring-slate-900 transition-colors duration-200;
  }

  .form-textarea {
    @apply form-input resize-none;
  }

  .form-select {
    @apply form-input pr-10 bg-white;
  }

  /* Professional Hero Section */
  .hero {
    @apply relative overflow-hidden bg-white;
  }

  .hero-content {
    @apply relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 sm:py-32;
  }

  .hero-title {
    @apply text-4xl sm:text-5xl lg:text-6xl font-bold text-slate-900 tracking-tight;
  }

  .hero-subtitle {
    @apply mt-6 text-xl text-slate-600 max-w-3xl;
  }

  .hero-actions {
    @apply mt-10 flex flex-col sm:flex-row gap-4;
  }

  /* Professional Search */
  .search-container {
    @apply relative max-w-2xl mx-auto;
  }

  .search-input {
    @apply w-full pl-12 pr-4 py-4 text-lg rounded-2xl border border-slate-300 focus:border-slate-900 focus:ring-1 focus:ring-slate-900 bg-white;
  }

  .search-icon {
    @apply absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5;
  }

  /* Professional Ratings */
  .rating-stars {
    @apply flex items-center space-x-1;
  }

  .rating-star {
    @apply w-5 h-5 text-slate-300 cursor-pointer transition-colors duration-150;
  }

  .rating-star.active {
    @apply text-amber-400;
  }

  .rating-star:hover {
    @apply text-amber-300;
  }

  /* Layout */
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .main-content {
    @apply pt-16 min-h-screen;
  }

  .section {
    @apply py-16 sm:py-24;
  }

  .section-header {
    @apply text-center mb-16;
  }

  .section-title {
    @apply text-3xl sm:text-4xl font-bold text-slate-900 tracking-tight;
  }

  .section-subtitle {
    @apply mt-4 text-lg text-slate-600 max-w-2xl mx-auto;
  }

  /* Professional Grid */
  .grid-1 { @apply grid grid-cols-1 gap-6; }
  .grid-2 { @apply grid grid-cols-1 md:grid-cols-2 gap-6; }
  .grid-3 { @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6; }
  .grid-4 { @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6; }

  /* Professional Animations */
  .fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .slide-up {
    animation: slideUp 0.6s ease-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Professional Flash Messages */
  .flash-message {
    @apply fixed top-20 right-4 z-50 max-w-sm p-4 rounded-xl shadow-lg border;
  }

  .flash-success {
    @apply bg-emerald-50 text-emerald-800 border-emerald-200;
  }

  .flash-error {
    @apply bg-red-50 text-red-800 border-red-200;
  }

  .flash-info {
    @apply bg-blue-50 text-blue-800 border-blue-200;
  }
}