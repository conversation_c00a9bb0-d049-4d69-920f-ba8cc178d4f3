import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["star", "input"]
  static values = { rating: Number }

  connect() {
    this.updateStars()
  }

  ratingValueChanged() {
    this.updateStars()
  }

  rate(event) {
    const rating = parseInt(event.currentTarget.dataset.rating)
    this.ratingValue = rating
    this.inputTarget.value = rating
    this.updateStars()
  }

  hover(event) {
    const rating = parseInt(event.currentTarget.dataset.rating)
    this.highlightStars(rating)
  }

  leave() {
    this.updateStars()
  }

  updateStars() {
    this.highlightStars(this.ratingValue)
  }

  highlightStars(rating) {
    this.starTargets.forEach((star, index) => {
      const starRating = index + 1
      if (starRating <= rating) {
        star.classList.add("text-yellow-400")
        star.classList.remove("text-gray-300")
      } else {
        star.classList.add("text-gray-300")
        star.classList.remove("text-yellow-400")
      }
    })
  }
}
