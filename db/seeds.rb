# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

puts "Creating categories..."

categories = [
  { name: "Restaurants", description: "Dining establishments, cafes, and food services" },
  { name: "Shops", description: "Retail stores and shopping centers" },
  { name: "Entertainment", description: "Cinemas, theaters, and entertainment venues" },
  { name: "Hotels", description: "Hotels, motels, and accommodation services" },
  { name: "Services", description: "Professional and personal services" },
  { name: "Health & Fitness", description: "Gyms, spas, and health services" },
  { name: "Education", description: "Schools, universities, and educational institutions" },
  { name: "Automotive", description: "Car dealerships, repair shops, and automotive services" }
]

categories.each do |category_attrs|
  Category.find_or_create_by!(name: category_attrs[:name]) do |category|
    category.description = category_attrs[:description]
  end
end

puts "Creating sample users..."

# Create sample users
users = [
  { email: "<EMAIL>", password: "password123", first_name: "<PERSON>", last_name: "<PERSON><PERSON>", bio: "Food enthusiast and local explorer" },
  { email: "<EMAIL>", password: "password123", first_name: "Jane", last_name: "Smith", bio: "Travel blogger and restaurant critic" },
  { email: "<EMAIL>", password: "password123", first_name: "Mike", last_name: "<PERSON>", bio: "Local business owner" }
]

users.each do |user_attrs|
  User.find_or_create_by!(email: user_attrs[:email]) do |user|
    user.password = user_attrs[:password]
    user.first_name = user_attrs[:first_name]
    user.last_name = user_attrs[:last_name]
    user.bio = user_attrs[:bio]
  end
end

puts "Creating sample entities..."

# Create sample entities
sample_entities = [
  {
    name: "The Gourmet Corner",
    description: "Fine dining restaurant with contemporary cuisine and excellent wine selection.",
    address: "123 Main Street, Downtown",
    phone: "(*************",
    website: "https://gourmetcorner.com",
    email: "<EMAIL>",
    category: "Restaurants"
  },
  {
    name: "Tech Haven Electronics",
    description: "Your one-stop shop for the latest electronics, gadgets, and tech accessories.",
    address: "456 Tech Avenue, Silicon Valley",
    phone: "(*************",
    website: "https://techhaven.com",
    email: "<EMAIL>",
    category: "Shops"
  },
  {
    name: "Starlight Cinema",
    description: "Modern movie theater with IMAX screens and premium seating options.",
    address: "789 Entertainment Blvd, City Center",
    phone: "(*************",
    website: "https://starlightcinema.com",
    email: "<EMAIL>",
    category: "Entertainment"
  },
  {
    name: "Grand Plaza Hotel",
    description: "Luxury hotel in the heart of the city with world-class amenities.",
    address: "321 Hotel Row, Business District",
    phone: "(*************",
    website: "https://grandplaza.com",
    email: "<EMAIL>",
    category: "Hotels"
  },
  {
    name: "FitLife Gym",
    description: "State-of-the-art fitness center with personal training and group classes.",
    address: "654 Fitness Street, Health District",
    phone: "(*************",
    website: "https://fitlifegym.com",
    email: "<EMAIL>",
    category: "Health & Fitness"
  }
]

sample_entities.each do |entity_attrs|
  category = Category.find_by(name: entity_attrs[:category])
  user = User.first # Assign to first user for simplicity

  Entity.find_or_create_by!(name: entity_attrs[:name]) do |entity|
    entity.description = entity_attrs[:description]
    entity.address = entity_attrs[:address]
    entity.phone = entity_attrs[:phone]
    entity.website = entity_attrs[:website]
    entity.email = entity_attrs[:email]
    entity.category = category
    entity.user = user
  end
end

puts "Creating sample reviews..."

# Temporarily disable callbacks during seeding
Review.skip_callback(:create, :after, :broadcast_new_review)
Review.skip_callback(:update, :after, :broadcast_updated_review)
Review.skip_callback(:destroy, :after, :broadcast_removed_review)

# Create sample reviews
Entity.all.each do |entity|
  User.all.sample(rand(1..3)).each do |user|
    next if entity.reviews.exists?(user: user)

    Review.create!(
      entity: entity,
      user: user,
      rating: rand(3..5),
      comment: [
        "Great experience! Highly recommended.",
        "Good service and quality. Will visit again.",
        "Excellent! Exceeded my expectations.",
        "Very satisfied with the service.",
        "Outstanding quality and friendly staff.",
        "Amazing place! Love coming here.",
        "Professional service and great atmosphere."
      ].sample
    )
  end
end

# Re-enable callbacks
Review.set_callback(:create, :after, :broadcast_new_review)
Review.set_callback(:update, :after, :broadcast_updated_review)
Review.set_callback(:destroy, :after, :broadcast_removed_review)

puts "Seed data created successfully!"
puts "Categories: #{Category.count}"
puts "Users: #{User.count}"
puts "Entities: #{Entity.count}"
puts "Reviews: #{Review.count}"
