class ConvertUsersToRailsAuth < ActiveRecord::Migration[8.0]
  def change
    # Remove Devise-specific columns
    remove_column :users, :encrypted_password, :string
    remove_column :users, :reset_password_token, :string
    remove_column :users, :reset_password_sent_at, :datetime
    remove_column :users, :remember_created_at, :datetime

    # Remove Devise indexes (if they exist)
    remove_index :users, :reset_password_token if index_exists?(:users, :reset_password_token)

    # Add Rails authentication column
    add_column :users, :password_digest, :string, null: false

    # Make required fields non-nullable
    change_column_null :users, :first_name, false
    change_column_null :users, :last_name, false
  end
end
