class CreateReviews < ActiveRecord::Migration[8.0]
  def change
    create_table :reviews do |t|
      t.integer :rating, null: false
      t.text :comment
      t.references :user, null: false, foreign_key: true
      t.references :entity, null: false, foreign_key: true

      t.timestamps
    end

    add_index :reviews, [ :user_id, :entity_id ], unique: true
    add_check_constraint :reviews, "rating >= 1 AND rating <= 5", name: "rating_range_check"
  end
end
