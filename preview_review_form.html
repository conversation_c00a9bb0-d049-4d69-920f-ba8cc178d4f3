<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudVision - Review Form Preview</title>
    <style>
        /* CSS Variables */
        :root {
          --primary: #0f172a;
          --primary-foreground: #f8fafc;
          --secondary: #f1f5f9;
          --secondary-foreground: #0f172a;
          --accent: #3b82f6;
          --accent-foreground: #ffffff;
          --muted: #f8fafc;
          --muted-foreground: #64748b;
          --border: #e2e8f0;
          --input: #ffffff;
          --ring: #3b82f6;
          --background: #ffffff;
          --foreground: #0f172a;
          --card: #ffffff;
          --card-foreground: #0f172a;
          --radius: 0.75rem;
        }

        * {
          border-color: var(--border);
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON>e UI', <PERSON><PERSON>, sans-serif;
          background: var(--background);
          color: var(--foreground);
          padding: 40px 20px;
        }

        /* Form System */
        .form-group {
          margin-bottom: 24px;
        }

        .form-label {
          display: block;
          font-weight: 600;
          font-size: 14px;
          color: var(--foreground);
          margin-bottom: 8px;
        }

        .form-input {
          width: 100%;
          padding: 12px 16px;
          border: 2px solid var(--border);
          border-radius: var(--radius);
          font-size: 16px;
          background: var(--input);
          color: var(--foreground);
          transition: all 0.2s ease;
        }

        .form-input:focus {
          outline: none;
          border-color: var(--accent);
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-textarea {
          resize: vertical;
          min-height: 120px;
        }

        /* Button System */
        .btn {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          border-radius: var(--radius);
          font-weight: 600;
          font-size: 14px;
          transition: all 0.2s ease;
          cursor: pointer;
          border: 1px solid transparent;
          text-decoration: none;
          position: relative;
          overflow: hidden;
          white-space: nowrap;
        }

        .btn:focus {
          outline: none;
          box-shadow: 0 0 0 2px var(--ring);
        }

        .btn-lg {
          padding: 16px 32px;
          font-size: 16px;
        }

        .btn-accent {
          background: var(--accent);
          color: var(--accent-foreground);
          border: 1px solid var(--accent);
        }

        .btn-accent:hover {
          background: #2563eb;
          border-color: #2563eb;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        /* Page Layout */
        .page-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 0 24px;
        }

        .page-header {
          margin-bottom: 40px;
        }

        .page-title {
          font-size: 32px;
          font-weight: 700;
          color: var(--foreground);
          margin-bottom: 8px;
        }

        .page-subtitle {
          font-size: 18px;
          color: var(--muted-foreground);
          line-height: 1.6;
        }

        /* Review Form Container */
        .review-form-container {
          margin-bottom: 40px;
          padding: 24px;
          background: var(--muted);
          border-radius: var(--radius);
          border: 1px solid var(--border);
        }

        /* Star Rating */
        .star-rating {
          display: flex;
          gap: 8px;
          padding: 12px 0;
        }

        .star-button {
          background: none;
          border: none;
          padding: 4px;
          cursor: pointer;
          color: #d1d5db;
          transition: all 0.2s ease;
          border-radius: 4px;
        }

        .star-button:hover {
          color: #f59e0b;
          transform: scale(1.1);
        }

        .star-button.active {
          color: #f59e0b;
        }

        /* Demo styles */
        .demo-section {
          margin-bottom: 60px;
        }

        .demo-title {
          font-size: 24px;
          font-weight: 600;
          margin-bottom: 20px;
          color: var(--foreground);
        }

        .demo-description {
          color: var(--muted-foreground);
          margin-bottom: 30px;
          line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="page-container">
        <div class="page-header">
            <h1 class="page-title">Review Form - Fixed Submit Button</h1>
            <p class="page-subtitle">Improved styling for the review form with better button design and layout</p>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">Improved Review Form</h2>
            <p class="demo-description">
                The review form now has better styling, improved button design, and enhanced user experience.
            </p>
        </div>

        <!-- Fixed Review Form -->
        <div class="review-form-container">
            <div style="margin-bottom: 24px;">
                <h3 style="font-size: 20px; font-weight: 600; color: var(--foreground); margin-bottom: 8px;">
                    Write a Review
                </h3>
                <p style="color: var(--muted-foreground); font-size: 14px;">
                    Share your experience to help others discover great places
                </p>
            </div>

            <form>
                <div class="form-group">
                    <label class="form-label">Rating</label>
                    <div class="star-rating">
                        <button type="button" class="star-button active">
                            <svg style="width: 32px; height: 32px;" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                        </button>
                        <button type="button" class="star-button active">
                            <svg style="width: 32px; height: 32px;" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                        </button>
                        <button type="button" class="star-button active">
                            <svg style="width: 32px; height: 32px;" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                        </button>
                        <button type="button" class="star-button active">
                            <svg style="width: 32px; height: 32px;" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                        </button>
                        <button type="button" class="star-button">
                            <svg style="width: 32px; height: 32px;" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Comment</label>
                    <textarea class="form-input form-textarea" placeholder="Share your experience..." rows="4">Great food and excellent service! The atmosphere was perfect for a date night. Highly recommend the pasta dishes.</textarea>
                </div>

                <div style="display: flex; justify-content: flex-end; margin-top: 32px; padding-top: 24px; border-top: 1px solid var(--border);">
                    <button type="submit" class="btn btn-accent btn-lg" style="min-width: 160px;">
                        Submit Review
                    </button>
                </div>
            </form>
        </div>

        <div style="margin-top: 40px; padding: 20px; background: var(--muted); border-radius: var(--radius);">
            <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 12px; color: var(--foreground);">
                ✅ Submit Button Improvements:
            </h3>
            <ul style="color: var(--muted-foreground); line-height: 1.8; padding-left: 20px;">
                <li><strong>Better Button Styling:</strong> Changed from btn-primary to btn-accent for better visibility</li>
                <li><strong>Improved Size:</strong> Using btn-lg for better prominence and touch targets</li>
                <li><strong>Enhanced Container:</strong> Added border-top separator and better spacing</li>
                <li><strong>Minimum Width:</strong> Set min-width: 160px for consistent button size</li>
                <li><strong>Form Container:</strong> Added background and border for better visual separation</li>
                <li><strong>Star Rating:</strong> Improved hover effects with scale animation</li>
                <li><strong>Form Header:</strong> Added title and description for better context</li>
            </ul>
        </div>
    </div>
</body>
</html>
