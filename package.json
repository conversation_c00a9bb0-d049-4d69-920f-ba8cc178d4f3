{"name": "cloudvision-app", "version": "1.0.0", "description": "This README would normally document whatever steps are necessary to get the application up and running.", "main": "index.js", "directories": {"lib": "lib", "test": "test"}, "scripts": {"build-css": "tailwindcss -i ./app/assets/tailwind/application.css -o ./app/assets/builds/application.css --watch", "build-css-prod": "tailwindcss -i ./app/assets/tailwind/application.css -o ./app/assets/builds/application.css --minify", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "tailwindcss": "^4.1.12"}}