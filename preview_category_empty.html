<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudVision - Category Empty State Preview</title>
    <style>
        /* CSS Variables */
        :root {
          --primary: #0f172a;
          --primary-foreground: #f8fafc;
          --secondary: #f1f5f9;
          --secondary-foreground: #0f172a;
          --accent: #3b82f6;
          --accent-foreground: #ffffff;
          --muted: #f8fafc;
          --muted-foreground: #64748b;
          --border: #e2e8f0;
          --input: #ffffff;
          --ring: #3b82f6;
          --background: #ffffff;
          --foreground: #0f172a;
          --card: #ffffff;
          --card-foreground: #0f172a;
          --radius: 0.75rem;
        }

        * {
          border-color: var(--border);
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
          background: var(--background);
          color: var(--foreground);
          padding: 40px 20px;
        }

        /* Card System */
        .card {
          background: var(--card);
          border: 1px solid var(--border);
          border-radius: var(--radius);
          box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
          transition: all 0.3s ease;
        }

        .card:hover {
          box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
          transform: translateY(-4px);
          border-color: rgba(59, 130, 246, 0.2);
        }

        .card-content {
          padding: 24px;
        }

        /* Button System */
        .btn {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          border-radius: var(--radius);
          font-weight: 600;
          font-size: 14px;
          transition: all 0.2s ease;
          cursor: pointer;
          border: 1px solid transparent;
          text-decoration: none;
          position: relative;
          overflow: hidden;
          white-space: nowrap;
        }

        .btn:focus {
          outline: none;
          box-shadow: 0 0 0 2px var(--ring);
        }

        .btn-lg {
          padding: 16px 32px;
          font-size: 16px;
        }

        .btn-accent {
          background: var(--accent);
          color: var(--accent-foreground);
          border: 1px solid var(--accent);
        }

        .btn-accent:hover {
          background: #2563eb;
          border-color: #2563eb;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        .btn-outline {
          background: transparent;
          color: var(--accent);
          border: 1px solid var(--accent);
        }

        .btn-outline:hover {
          background: var(--accent);
          color: var(--accent-foreground);
          transform: translateY(-1px);
        }

        /* Page Layout */
        .page-container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 24px;
        }

        .page-header {
          margin-bottom: 40px;
        }

        .page-title {
          font-size: 32px;
          font-weight: 700;
          color: var(--foreground);
          margin-bottom: 8px;
        }

        .page-subtitle {
          font-size: 18px;
          color: var(--muted-foreground);
          line-height: 1.6;
        }

        /* Demo styles */
        .demo-section {
          margin-bottom: 60px;
        }

        .demo-title {
          font-size: 24px;
          font-weight: 600;
          margin-bottom: 20px;
          color: var(--foreground);
        }

        .demo-description {
          color: var(--muted-foreground);
          margin-bottom: 30px;
          line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="page-container">
        <div class="page-header">
            <h1 class="page-title">Category Empty State - Fixed</h1>
            <p class="page-subtitle">Improved styling for the "No places in this category yet" page</p>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">Before vs After</h2>
            <p class="demo-description">
                The category detail page now uses consistent styling with proper card layout, 
                improved empty state design, and better button placement.
            </p>
        </div>

        <!-- Fixed Empty State -->
        <div class="card">
            <div class="card-content" style="text-align: center; padding: 64px 32px;">
                <div style="font-size: 80px; margin-bottom: 24px;">🍽️</div>
                <h3 style="font-size: 24px; font-weight: 600; color: var(--foreground); margin-bottom: 12px;">
                    No places in this category yet
                </h3>
                <p style="color: var(--muted-foreground); margin-bottom: 32px; max-width: 400px; margin-left: auto; margin-right: auto;">
                    Be the first to add a place in the restaurants category and help others discover great spots!
                </p>
                
                <!-- Signed In State -->
                <div style="margin-bottom: 40px;">
                    <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 16px; color: var(--foreground);">
                        For Signed In Users:
                    </h4>
                    <a href="#" class="btn btn-accent btn-lg">
                        <svg style="width: 20px; height: 20px; margin-right: 8px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        Add First Place
                    </a>
                </div>

                <!-- Not Signed In State -->
                <div>
                    <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 16px; color: var(--foreground);">
                        For Non-Signed In Users:
                    </h4>
                    <div style="display: flex; gap: 12px; justify-content: center; flex-wrap: wrap;">
                        <a href="#" class="btn btn-accent btn-lg">Sign In</a>
                        <a href="#" class="btn btn-outline btn-lg">Sign Up</a>
                    </div>
                </div>
            </div>
        </div>

        <div style="margin-top: 40px; padding: 20px; background: var(--muted); border-radius: var(--radius);">
            <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 12px; color: var(--foreground);">
                ✅ Improvements Made:
            </h3>
            <ul style="color: var(--muted-foreground); line-height: 1.8; padding-left: 20px;">
                <li><strong>Consistent Card Layout:</strong> Uses the custom card system instead of Tailwind classes</li>
                <li><strong>Better Empty State:</strong> Larger icon, improved typography, and better spacing</li>
                <li><strong>Enhanced Buttons:</strong> Proper button classes with icons and hover effects</li>
                <li><strong>User State Handling:</strong> Different content for signed-in vs non-signed-in users</li>
                <li><strong>Responsive Design:</strong> Buttons stack properly on mobile devices</li>
                <li><strong>Visual Hierarchy:</strong> Clear information hierarchy with proper spacing</li>
            </ul>
        </div>
    </div>
</body>
</html>
