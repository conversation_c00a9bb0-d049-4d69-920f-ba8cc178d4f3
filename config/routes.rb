Rails.application.routes.draw do
  get "registrations/new"
  get "registrations/create"
  get "sessions/new"
  get "sessions/create"
  get "sessions/destroy"
  root "home#index"

  # Authentication routes
  get  "sign_in", to: "sessions#new"
  post "sign_in", to: "sessions#create"
  get  "sign_up", to: "registrations#new"
  post "sign_up", to: "registrations#create"
  delete "logout", to: "sessions#destroy"

  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html
  resources :entities do
    resources :reviews, except: [ :index, :show ]
    member do
      get :reviews, to: "entities#reviews"
    end
  end

  resources :categories, only: [ :index, :show ]

  # Search route
  get "search", to: "entities#search"

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker
end
